import { Star, Image, Settings, MessageSquare, Users, Link, Share2, MapPin, Palette } from 'lucide-react'
import { DataSection, DataField } from './types'
import { validateHexColor, validatePhone, validateDescription, validateKeywords } from './validation'

// Data section definitions based on the JSON structure
export const dataSections: DataSection[] = [
  {
    id: 'featuresSection',
    name: 'Features',
    type: 'object',
    itemsKey: 'items',
    icon: Star,
    description: 'Manage feature highlights',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'gallery',
    name: 'Gallery',
    type: 'object',
    itemsKey: 'images',
    icon: Image,
    description: 'Manage gallery images',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'servicesSection',
    name: 'Services',
    type: 'object',
    itemsKey: 'items',
    icon: Settings,
    description: 'Manage services offered',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'reviews',
    name: 'Reviews',
    type: 'object',
    itemsKey: 'reviews',
    icon: MessageSquare,
    description: 'Manage customer reviews',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'team',
    name: 'Team',
    type: 'object',
    itemsKey: 'members',
    icon: Users,
    description: 'Manage team members',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
    ]
  },
  {
    id: 'links',
    name: 'Links',
    type: 'array',
    icon: Link,
    description: 'Manage profile links',
    fields: [
      { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
      { key: 'text', label: 'Text', type: 'string', required: true },
      { key: 'url', label: 'URL', type: 'url', required: true },
    ]
  },
  {
    id: 'socialMedia',
    name: 'Social Media',
    type: 'array',
    icon: Share2,
    description: 'Manage social media links',
    fields: [
      { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
      { key: 'text', label: 'Text', type: 'string', required: true },
      { key: 'url', label: 'URL', type: 'url', required: true },
    ]
  },
  {
    id: 'location',
    name: 'Location',
    type: 'object',
    icon: MapPin,
    description: 'Manage location info',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'address.street', label: 'Street Address', type: 'string', required: true },
      { key: 'address.city', label: 'City', type: 'string', required: true },
      { key: 'address.state', label: 'State/Province', type: 'string', required: true },
      { key: 'address.zipCode', label: 'ZIP/Postal Code', type: 'string', required: true },
      { key: 'address.country', label: 'Country', type: 'string', required: true },
      {
        key: 'contact.phone',
        label: 'Phone Number',
        type: 'string',
        validation: validatePhone
      },
      { key: 'contact.whatsapp', label: 'WhatsApp URL', type: 'url' },
      { key: 'hours.weekdays', label: 'Weekday Hours', type: 'string' },
      { key: 'hours.weekends', label: 'Weekend Hours', type: 'string' },
      { key: 'hours.closed', label: 'Closed Days', type: 'string' },
      { key: 'googleMapsUrl', label: 'Google Maps URL', type: 'url' },
    ]
  },
  {
    id: 'settings',
    name: 'Settings',
    type: 'object',
    icon: Palette,
    description: 'Manage profile settings',
    fields: [
      {
        key: 'colors.background',
        label: 'Background Color',
        type: 'string',
        validation: validateHexColor
      },
      {
        key: 'colors.linkText',
        label: 'Link Text Color',
        type: 'string',
        validation: validateHexColor
      },
      {
        key: 'colors.primary',
        label: 'Primary Color',
        type: 'string',
        validation: validateHexColor
      },
      {
        key: 'colors.secondary',
        label: 'Secondary Color',
        type: 'string',
        validation: validateHexColor
      },
      {
        key: 'colors.socialIconBackground',
        label: 'Social Icon Background',
        type: 'string',
        validation: validateHexColor
      },
      { key: 'favicon', label: 'Favicon URL', type: 'url' },
      {
        key: 'pageDescription',
        label: 'Page Description',
        type: 'textarea',
        validation: validateDescription
      },
      {
        key: 'pageKeywords',
        label: 'Page Keywords',
        type: 'string',
        validation: validateKeywords
      },
    ]
  },
]

// Item field definitions for array-type sections
export const itemFieldDefinitions: Record<string, DataField[]> = {
  featuresSection: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'image', label: 'Image URL', type: 'url', required: true },
  ],
  gallery: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'alt', label: 'Alt Text', type: 'string', required: true },
    { key: 'url', label: 'Image URL', type: 'url', required: true },
  ],
  servicesSection: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'image', label: 'Image URL', type: 'url', required: true },
  ],
  reviews: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'name', label: 'Name', type: 'string', required: true },
    { key: 'comment', label: 'Comment', type: 'textarea', required: true },
    { key: 'rating', label: 'Rating', type: 'number', required: true },
    { key: 'photo', label: 'Photo URL', type: 'url' },
  ],
  team: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'name', label: 'Name', type: 'string', required: true },
    { key: 'role', label: 'Role', type: 'string', required: true },
    { key: 'photo', label: 'Photo URL', type: 'url' },
    { key: 'url', label: 'Profile URL', type: 'url' },
  ],
  links: [
    { key: 'id', label: 'ID', type: 'number' },
    { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
    { key: 'text', label: 'Text', type: 'string', required: true },
    { key: 'url', label: 'URL', type: 'url', required: true },
  ],
  socialMedia: [
    { key: 'id', label: 'ID', type: 'number' },
    { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
    { key: 'text', label: 'Text', type: 'string', required: true },
    { key: 'url', label: 'URL', type: 'url', required: true },
  ],
}
