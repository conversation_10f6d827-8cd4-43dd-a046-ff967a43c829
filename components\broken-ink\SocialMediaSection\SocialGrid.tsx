import React from "react";
import { getIconComponent } from "@/lib/iconUtils";
import { SocialCard } from "./SocialCard";
import { SocialPlatform } from "./types";

interface SocialGridProps {
  platforms: SocialPlatform[];
}

export const SocialGrid: React.FC<SocialGridProps> = ({ platforms }) => {
  return (
    <div className="flex flex-wrap items-center justify-center gap-6 mx-auto">
      {platforms.map((platform) => {
        const IconComponent = getIconComponent(platform.iconName);
        return (
          <SocialCard
            key={platform.platformName}
            icon={
              <IconComponent
                className="h-6 w-6 text-white"
                aria-label={platform.platformName}
              />
            }
            platformName={platform.platformName}
            description={platform.description}
            href={platform.href}
          />
        );
      })}
    </div>
  );
};
