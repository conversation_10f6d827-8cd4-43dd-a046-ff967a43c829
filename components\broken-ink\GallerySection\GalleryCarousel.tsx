"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { GalleryCarouselProps } from "./types";
import { GalleryImage } from "./GalleryImage";
import { NavigationControls } from "../shared/NavigationControls";
import { ProgressIndicator } from "../shared/ProgressIndicator";

// Import PhotoSwipe styles
import "photoswipe/dist/photoswipe.css";

// Import PhotoSwipe
import PhotoSwipeLightbox from "photoswipe/lightbox";

export const GalleryCarousel: React.FC<GalleryCarouselProps> = ({ items }) => {
  const galleryRef = useRef<HTMLDivElement>(null);
  const [imageDimensions, setImageDimensions] = useState<{
    [key: string]: { width: number; height: number };
  }>({});
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    loop: true,
    slidesToScroll: 1,
  });

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  const scrollTo = useCallback(
    (index: number) => {
      if (emblaApi) emblaApi.scrollTo(index);
    },
    [emblaApi]
  );

  // Set up embla event listeners
  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);

    return () => {
      try {
        emblaApi.off("select", onSelect);
        emblaApi.off("reInit", onSelect);
      } catch {
        // no-op if emblaApi instance was disposed
      }
    };
  }, [emblaApi, onSelect]);

  // Initialize PhotoSwipe
  useEffect(() => {
    let lightbox: PhotoSwipeLightbox | null = null;

    if (
      galleryRef.current &&
      items.length > 0 &&
      Object.keys(imageDimensions).length > 0
    ) {
      lightbox = new PhotoSwipeLightbox({
        gallery: "#photo-gallery",
        children: "a",
        pswpModule: () => import("photoswipe"),
        // Options
        showHideAnimationType: "fade",
        showAnimationDuration: 300,
        hideAnimationDuration: 300,
        zoom: true,
        counter: true,
        close: true,
        imageClickAction: "zoom",
        tapAction: "zoom",
      });

      lightbox.init();
    }

    return () => {
      if (lightbox) {
        lightbox.destroy();
      }
    };
  }, [items, imageDimensions]);

  // Preload images and get their real dimensions
  useEffect(() => {
    const loadImageDimensions = async () => {
      const dimensions: { [key: string]: { width: number; height: number } } =
        {};

      await Promise.all(
        items.map((item) => {
          return new Promise<void>((resolve) => {
            const img = new window.Image();
            img.onload = () => {
              dimensions[item.imageUrl] = {
                width: img.naturalWidth,
                height: img.naturalHeight,
              };
              resolve();
            };
            img.onerror = () => {
              // Fallback dimensions if image fails to load
              dimensions[item.imageUrl] = {
                width: 1200,
                height: 800,
              };
              resolve();
            };
            img.src = item.imageUrl;
          });
        })
      );

      setImageDimensions(dimensions);
    };

    if (items.length > 0) {
      loadImageDimensions();
    }
  }, [items]);

  return (
    <div className="relative" ref={galleryRef} id="photo-gallery">
      <div className="overflow-hidden rounded-xl sm:rounded-2xl" ref={emblaRef}>
        <div className="flex">
          {items.map((item, index) => (
            <div
              key={`${item.title}-${index}`}
              className="flex-[0_0_100%] sm:flex-[0_0_50%] lg:flex-[0_0_33.333%] min-w-0 px-1 sm:px-2"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <GalleryImage
                item={item}
                index={index}
                totalItems={items.length}
                imageDimensions={imageDimensions}
              />
            </div>
          ))}
        </div>
      </div>

      <ProgressIndicator
        count={items.length}
        selectedIndex={selectedIndex}
        onSelect={scrollTo}
        ariaLabel="Navigate to gallery image"
      />

      <NavigationControls
        onPrevious={scrollPrev}
        onNext={scrollNext}
        previousLabel="Imagem anterior"
        nextLabel="Próxima imagem"
      />
    </div>
  );
};
