'use client'

import { useState, useEffect } from 'react'
import { UserProfile, UserNotFoundError, InvalidUsernameError } from '@/types'
import { apiService } from '@/services/api'

interface UseUserProfileReturn {
  profile: UserProfile | null
  loading: boolean
  error: string | null
  notFound: boolean
  invalidUsername: boolean
  refetch: () => Promise<void>
}

export function useUserProfile(username: string): UseUserProfileReturn {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [notFound, setNotFound] = useState(false)
  const [invalidUsername, setInvalidUsername] = useState(false)

  const fetchProfile = async () => {
    if (!username) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      setNotFound(false)
      setInvalidUsername(false)

      const profileData = await apiService.getUserProfile(username)
      setProfile(profileData)

      // Track profile view
      await apiService.incrementProfileView(username).catch(() => {
        // Silently fail for analytics
      })
    } catch (err) {
      if (err instanceof UserNotFoundError) {
        setNotFound(true)
      } else if (err instanceof InvalidUsernameError) {
        setInvalidUsername(true)
      } else {
        setError(err instanceof Error ? err.message : 'Failed to load profile')
      }
      setProfile(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProfile()
  }, [username])

  return {
    profile,
    loading,
    error,
    notFound,
    invalidUsername,
    refetch: fetchProfile,
  }
}
