import {
    Mail,
    Phone,
    Share2,
    <PERSON>Link,
    <PERSON>rush,
    <PERSON><PERSON>,
    <PERSON>freshCw,
    MapPin,
    Calendar,
    Eye,
    ArrowUpRight,
} from "lucide-react";
import {
    FaFacebook,
    FaInstagram,
    FaWhatsapp,
    FaTwitter,
    FaYoutube,
    FaLinkedin,
    FaGithub,
    FaPinterest,
    FaTiktok,
    FaDiscord,
    FaReddit,
    FaGlobe,
    FaTelegram, FaApple, FaAppStore, FaGooglePlay,
    FaSpotify
} from "react-icons/fa";
/**
 * Maps icon names to their corresponding Lucide React components
 * Handles various icon name formats including Font Awesome prefixes
 *
 * @param iconName - The icon name to map (supports fa-, fab-, fas-, far- prefixes)
 * @returns The corresponding React component or ExternalLink as fallback
 */
export const getIconComponent = (iconName: string): React.ComponentType<React.SVGProps<SVGSVGElement>> => {
    const iconMap: Record<
        string,
        React.ComponentType<React.SVGProps<SVGSVGElement>>
    > = {
        facebook: FaFacebook,
        twitter: FaTwitter,
        instagram: FaInstagram,
        linkedin: FaLinkedin,
        youtube: FaYoutube,
        github: FaGithub,
        globe: FaGlobe,
        website: FaGlobe,
        mail: Mail,
        email: Mail,
        phone: Phone,
        whatsapp: FaWhatsapp,
        telegram: FaTelegram,
        tiktok: FaTiktok,
        pinterest: FaPinterest,
        discord: FaDiscord,
        reddit: FaReddit,
        share: Share2,
        link: ExternalLink,
        external: ExternalLink,
        brush: Brush,
        tattoo: Brush,
        custom: Brush,
        palette: Palette,
        design: Palette,
        designs: Palette,
        refresh: RefreshCw,
        coverup: RefreshCw,
        coverups: RefreshCw,
        map: MapPin,
        apple: FaApple,
        appstore: FaAppStore,
        googleplay: FaGooglePlay,
        calendar: Calendar,
        eye: Eye,
        spotify: FaSpotify
    };

    // Clean the icon name by removing Font Awesome prefixes and converting to lowercase
    // Handle formats like "fa fa-whatsapp", "fab fa-instagram", "fas fa-phone", etc.
    const cleanName = iconName
        .replace(
            /^(fa\s+fa-|fab\s+fa-|fas\s+fa-|far\s+fa-|icon-|fa-|fab-|fas-|far-)/i,
            ""
        )
        .toLowerCase()
        .trim();

    return iconMap[cleanName] || ArrowUpRight;
};