import { NextRequest, NextResponse } from 'next/server';
import { extractLinktreeData } from '@/lib/linktreeExtractor';

export async function POST(req: NextRequest) {
  try {
    const { url } = await req.json();

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    const extractResult = await extractLinktreeData(url);

    return NextResponse.json(extractResult);
  } catch (error) {
    // Log only safe error information to prevent sensitive data exposure
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('Error extracting Linktree data:', {
      message: errorMessage,
      timestamp: new Date().toISOString(),
      endpoint: '/api/linktree'
    });
    return NextResponse.json({ error: 'Failed to extract Linktree data' }, { status: 500 });
  }
}
