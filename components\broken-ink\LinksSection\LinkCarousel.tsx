import React, { useState, useEffect } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { getIconComponent } from "@/lib/iconUtils";
import { LinkCard } from "./LinkCard";
import { ProgressIndicator } from "../shared/ProgressIndicator";
import { useCarousel } from "../../hooks/useCarousel";
import { TransformedLink } from "./types";

interface LinkCarouselProps {
  links: TransformedLink[];
  onLinkClick: (url: string, event: React.MouseEvent) => void;
  isLoading: boolean;
}

export const LinkCarousel: React.FC<LinkCarouselProps> = ({
  links,
  onLinkClick,
  isLoading,
}) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: true,
    skipSnaps: false,
    slidesToScroll: "auto",
  });

  const { selectedIndex, scrollTo } = useCarousel(emblaApi);
  const [screenWidth, setScreenWidth] = useState(0);

  // Track screen width for responsive dot calculation
  useEffect(() => {
    const updateScreenWidth = () => {
      setScreenWidth(window.innerWidth);
    };

    // Set initial width
    updateScreenWidth();

    window.addEventListener("resize", updateScreenWidth);
    return () => window.removeEventListener("resize", updateScreenWidth);
  }, []);

  // Calculate how many slides are visible based on screen size
  const getVisibleSlides = () => {
    if (screenWidth === 0) return 1; // Default to 1 during SSR

    const gap = 16; // gap-4 = 16px
    let slideWidth: number;

    // Determine slide width based on breakpoints
    if (screenWidth >= 768) {
      // md breakpoint
      slideWidth = 380;
    } else if (screenWidth >= 640) {
      // sm breakpoint
      slideWidth = 360;
    } else {
      slideWidth = 320;
    }

    // Calculate how many slides fit in the container
    // Assuming container takes most of the screen width with some padding
    const containerWidth = screenWidth - 64; // Account for padding
    const slidesVisible = Math.floor(containerWidth / (slideWidth + gap));

    return Math.max(1, slidesVisible);
  };

  // Calculate the correct number of dots
  const getDotCount = () => {
    const totalSlides = links.length;
    const visibleSlides = getVisibleSlides();

    // If all slides are visible, show only 1 dot
    if (visibleSlides >= totalSlides) {
      return 1;
    }

    // Calculate scroll positions: total slides - visible slides + 1
    return totalSlides - visibleSlides + 1;
  };

  return (
    <div className="relative">
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex gap-4 touch-pan-y">
          {links.map((link, index) => {
            const IconComponent = getIconComponent(link.iconName);
            return (
              <div
                key={index}
                className="flex-none w-[320px] sm:w-[360px] md:w-[380px] p-2"
              >
                <LinkCard
                  icon={
                    !isLoading ? (
                      <IconComponent
                        className="h-6 w-6 text-white"
                        aria-hidden="true"
                        focusable="false"
                      />
                    ) : null
                  }
                  title={link.text}
                  description={link.description}
                  onClick={(e) => onLinkClick(link.url, e)}
                  isLoading={isLoading}
                />
              </div>
            );
          })}
        </div>
      </div>

      {!isLoading && (
        <ProgressIndicator
          count={getDotCount()}
          selectedIndex={selectedIndex}
          onSelect={scrollTo}
          className="flex justify-center mt-6 gap-2"
          useButtons={true}
          ariaLabel="Ir para link"
        />
      )}
    </div>
  );
};
