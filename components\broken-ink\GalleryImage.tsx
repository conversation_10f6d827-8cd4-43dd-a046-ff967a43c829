import React from 'react';

interface GalleryImageProps {
  imageUrl: string;
  altText: string; // For accessibility, alt text is important
  title: string;
}

const GalleryImage: React.FC<GalleryImageProps> = ({ imageUrl, altText, title }) => {
  return (
    <div className="group relative overflow-hidden rounded-2xl">
      <div
        className="w-full h-80 bg-center bg-cover transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url("${imageUrl}")` }}
        role="img" // Accessibility: identify this div as an image
        aria-label={altText} // Accessibility: provide alt text
      ></div>
      <div className="absolute inset-0 bg-black/40 flex items-end p-6">
        <p className="text-white text-lg font-medium">{title}</p>
      </div>
    </div>
  );
};

export default GalleryImage;
