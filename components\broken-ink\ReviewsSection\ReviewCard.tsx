import React from "react";
import { Star } from "lucide-react";
import { ReviewCardProps } from "./types";

export const ReviewCard = ({ review, index }: ReviewCardProps) => {
  return (
    <div
      className="flex-[0_0_calc(100%-2rem)] sm:flex-[0_0_calc(70%-1rem)] md:flex-[0_0_calc(50%-1rem)] lg:flex-[0_0_360px] min-w-0 max-w-full"
      style={{ animationDelay: `${index * 0.1}s` }}
    >
      <div className="bg-custom rounded-3xl p-4 sm:p-6 h-full flex flex-col border border-gray-950 transition-all duration-300 hover:scale-[1.02] transform-gpu w-full">
        <div className="flex items-start space-x-4 mb-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <h4 className="font-semibold text-white text-base truncate">
                {review.name}
              </h4>
              <div
                className="flex text-yellow-400 shrink-0"
                aria-label={`${review.rating} estrelas`}
              >
                {[...Array(review.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="text-sm transition-transform duration-300 text-yellow-400 fill-current"
                    style={{ animationDelay: `${i * 0.1}s` }}
                    aria-hidden="true"
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Review comment */}
        <blockquote className="flex-1 flex items-center">
          <p className="text-gray-300 text-sm leading-relaxed italic">
            &ldquo;{review.comment}&rdquo;
          </p>
        </blockquote>

        {/* Decorative quote mark */}
        <div className="mt-4 flex justify-end opacity-20 transition-opacity duration-300">
          <i className="fas fa-quote-right text-2xl text-gray-500"></i>
        </div>
      </div>
    </div>
  );
};
