"use client";

import React from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface ErrorAlertProps {
  error: string;
  onDismiss?: () => void;
  className?: string;
}

export default function ErrorAlert({ error, onDismiss, className }: ErrorAlertProps) {
  return (
    <Alert
      variant="destructive"
      className={`animate-slide-down border-red-200 bg-red-50 dark:bg-red-950/50 ${className}`}
      role="alert"
      aria-live="assertive"
      aria-atomic="true"
    >
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <span className="flex-1">{error}</span>
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="h-auto p-1 hover:bg-red-100 dark:hover:bg-red-900/50"
          >
            <X className="h-3 w-3" />
            <span className="sr-only">Dismiss error</span>
          </Button>
        )}
      </AlertDescription>
    </Alert>
  );
}