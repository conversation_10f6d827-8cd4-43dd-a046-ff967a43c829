"use client";

import React from "react";
import Image from "next/image";
import { Eye } from "lucide-react";
import { GalleryImageProps } from "./types";

export const GalleryImage: React.FC<GalleryImageProps> = ({
  item,
  index,
  totalItems,
  imageDimensions,
}) => {
  return (
    <div className="overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.02] transform-gpu group rounded-lg">
      <a
        href={item.imageUrl}
        data-pswp-width={imageDimensions[item.imageUrl]?.width || 1200}
        data-pswp-height={imageDimensions[item.imageUrl]?.height || 800}
        data-pswp-caption={`<h4>${item.title}</h4><p>${item.altText}</p>`}
        target="_blank"
        rel="noreferrer"
        className="block relative group cursor-pointer"
      >
        <Image
          src={item.imageUrl}
          alt={item.altText}
          width={imageDimensions[item.imageUrl]?.width || 1200}
          height={imageDimensions[item.imageUrl]?.height || 800}
          className="w-full h-64 sm:h-80 lg:h-96 object-cover transition-transform duration-700 group-hover:scale-110"
          priority={index === 0}
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 100vw"
        />

        {/* Enhanced overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-end">
          <div className="p-4 sm:p-6 lg:p-8 text-white w-full transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-2 drop-shadow-lg">
              {item.title}
            </h3>
            <p className="text-sm sm:text-base opacity-90 leading-relaxed drop-shadow-sm">
              {item.altText}
            </p>
          </div>
        </div>

        {/* View icon overlay */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
            <Eye className="w-6 h-6 text-white" aria-hidden="true" />
          </div>
        </div>

        {/* Image counter badge */}
        <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm rounded-full px-3 py-1">
          <span className="text-white text-xs sm:text-sm font-medium">
            {index + 1} / {totalItems}
          </span>
        </div>
      </a>
    </div>
  );
};
