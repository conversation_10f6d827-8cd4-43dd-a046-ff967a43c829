import { TemplateOption } from './types';

// Template options for the API
export const templateOptions: TemplateOption[] = [
  {
    value: "general",
    label: "General",
    description: "Default template for any type of business or profile",
  },
  {
    value: "tattoo",
    label: "Tattoo Studio",
    description:
      "Specialized for tattoo studios, tattoo artists and body art professionals",
  },
  {
    value: "barber",
    label: "Barber Shop",
    description:
      "Specialized for barber shops, barbers and men's beauty salons",
  },
  {
    value: "gym",
    label: "GYM",
    description: "Specialized for gyms, crossfit and fitness centers",
  },
];

// API endpoints
export const API_ENDPOINTS = {
  EXTRACT: "/api/linktree",
  PROCESS: process.env.NEXT_PUBLIC_LINKTREE_PROCESS_API || "https://side-projects-linktree-extractor.vhhb1z.easypanel.host/api/v1/scrape/process",
} as const;

// Pre-defined template text
export const PREDEFINED_TEMPLATE_TEXT =
  "URL: | \nUSERNAME: | \nDESCRIÇÃO/BIO: | \nLOCALIZAÇÃO: | \nINSTAGRAM: | \nEQUIPE: | \nOGIMAGE: | \nBGIMAGE: \nREVIEW 1: | \nREVIEW 2: | \nREVIEW 3: | \nREVIEW 4: | \nREVIEW 5: |";
