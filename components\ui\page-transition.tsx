"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'fade' | 'slide' | 'scale' | 'blur' | 'none';
  duration?: number;
}

const transitionVariants = {
  fade: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  slide: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
  },
  scale: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 1.05 },
  },
  blur: {
    initial: { opacity: 0, filter: "blur(4px)" },
    animate: { opacity: 1, filter: "blur(0px)" },
    exit: { opacity: 0, filter: "blur(4px)" },
  },
  none: {
    initial: {},
    animate: {},
    exit: {},
  },
};

export function PageTransition({ 
  children, 
  className, 
  variant = 'fade',
  duration = 0.3 
}: PageTransitionProps) {
  const pathname = usePathname();

  return (
    <AnimatePresence mode="wait" initial={false}>
      <motion.div
        key={pathname}
        className={cn("w-full", className)}
        variants={transitionVariants[variant]}
        initial="initial"
        animate="animate"
        exit="exit"
        transition={{
          duration,
          ease: "easeInOut",
        }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
}

// Stagger animation for lists
interface StaggerContainerProps {
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
  duration?: number;
}

export function StaggerContainer({ 
  children, 
  className, 
  staggerDelay = 0.1,
  duration = 0.3 
}: StaggerContainerProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
        duration,
      },
    },
  };

  return (
    <motion.div
      className={className}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {children}
    </motion.div>
  );
}

// Individual stagger item
interface StaggerItemProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'fade' | 'slide' | 'scale';
  duration?: number;
}

export function StaggerItem({ 
  children, 
  className, 
  variant = 'fade',
  duration = 0.3 
}: StaggerItemProps) {
  const itemVariants = {
    fade: {
      hidden: { opacity: 0 },
      visible: { opacity: 1 },
    },
    slide: {
      hidden: { opacity: 0, y: 20 },
      visible: { opacity: 1, y: 0 },
    },
    scale: {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { opacity: 1, scale: 1 },
    },
  };

  return (
    <motion.div
      className={className}
      variants={itemVariants[variant]}
      transition={{ duration }}
    >
      {children}
    </motion.div>
  );
}

// Scroll reveal animation
interface ScrollRevealProps {
  children: React.ReactNode;
  className?: string;
  threshold?: number;
  variant?: 'fade' | 'slide' | 'scale';
  direction?: 'up' | 'down' | 'left' | 'right';
  duration?: number;
  delay?: number;
}

export function ScrollReveal({ 
  children, 
  className, 
  threshold = 0.1,
  variant = 'fade',
  direction = 'up',
  duration = 0.6,
  delay = 0 
}: ScrollRevealProps) {
  const getInitialState = () => {
    switch (variant) {
      case 'slide':
        switch (direction) {
          case 'up': return { opacity: 0, y: 50 };
          case 'down': return { opacity: 0, y: -50 };
          case 'left': return { opacity: 0, x: 50 };
          case 'right': return { opacity: 0, x: -50 };
          default: return { opacity: 0, y: 50 };
        }
      case 'scale':
        return { opacity: 0, scale: 0.8 };
      default:
        return { opacity: 0 };
    }
  };

  const getAnimateState = () => {
    switch (variant) {
      case 'slide':
        return { opacity: 1, x: 0, y: 0 };
      case 'scale':
        return { opacity: 1, scale: 1 };
      default:
        return { opacity: 1 };
    }
  };

  return (
    <motion.div
      className={className}
      initial={getInitialState()}
      whileInView={getAnimateState()}
      viewport={{ once: true, amount: threshold }}
      transition={{
        duration,
        delay,
        ease: "easeOut",
      }}
    >
      {children}
    </motion.div>
  );
}

// Hover animation wrapper
interface HoverAnimationProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'lift' | 'scale' | 'rotate' | 'glow' | 'bounce';
  intensity?: 'subtle' | 'medium' | 'strong';
}

export function HoverAnimation({ 
  children, 
  className, 
  variant = 'lift',
  intensity = 'medium' 
}: HoverAnimationProps) {
  const getHoverAnimation = () => {
    const intensityMap = {
      subtle: { lift: 2, scale: 1.02, rotate: 2, glow: 0.1, bounce: 1.02 },
      medium: { lift: 4, scale: 1.05, rotate: 5, glow: 0.2, bounce: 1.05 },
      strong: { lift: 8, scale: 1.1, rotate: 10, glow: 0.3, bounce: 1.1 },
    };

    const values = intensityMap[intensity];

    switch (variant) {
      case 'lift':
        return { y: -values.lift, transition: { duration: 0.2 } };
      case 'scale':
        return { scale: values.scale, transition: { duration: 0.2 } };
      case 'rotate':
        return { rotate: values.rotate, transition: { duration: 0.2 } };
      case 'glow':
        return { 
          boxShadow: `0 0 20px rgba(59, 130, 246, ${values.glow})`,
          transition: { duration: 0.2 }
        };
      case 'bounce':
        return { 
          scale: values.bounce,
          transition: { 
            type: "spring",
            stiffness: 400,
            damping: 10
          }
        };
      default:
        return {};
    }
  };

  return (
    <motion.div
      className={className}
      whileHover={getHoverAnimation()}
      whileTap={{ scale: 0.98 }}
    >
      {children}
    </motion.div>
  );
}

// Loading animation
interface LoadingAnimationProps {
  variant?: 'pulse' | 'bounce' | 'spin' | 'wave';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingAnimation({ 
  variant = 'pulse', 
  size = 'md',
  className 
}: LoadingAnimationProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  const animations = {
    pulse: {
      scale: [1, 1.2, 1],
      transition: {
        duration: 1,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
    bounce: {
      y: [0, -10, 0],
      transition: {
        duration: 0.6,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
    spin: {
      rotate: 360,
      transition: {
        duration: 1,
        repeat: Infinity,
        ease: "linear",
      },
    },
    wave: {
      scale: [1, 1.1, 1],
      opacity: [1, 0.7, 1],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  return (
    <motion.div
      className={cn(
        "rounded-full bg-primary",
        sizeClasses[size],
        className
      )}
      animate={animations[variant]}
    />
  );
}
