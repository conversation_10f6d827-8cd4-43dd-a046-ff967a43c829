import React from "react";
import { cn } from "@/lib/utils";

interface SectionHeaderProps {
  title?: string;
  description?: string;
  className?: string;
  titleClassName?: string;
  descriptionClassName?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  description,
  className,
  titleClassName,
  descriptionClassName,
}) => {
  if (!title && !description) return null;

  return (
    <div className={cn("text-center", className)}>
      {title && (
        <h2
          className={cn(
            "text-3xl sm:text-4xl font-medium tracking-tight text-white",
            // "text-gradient-primary gradient-text-fallback",
            titleClassName
          )}
        >
          {title}
        </h2>
      )}
      {description && (
        <p
          className={cn(
            "mt-2 text-xl leading-8 max-w-2xl mx-auto text-gray-200",
            // "text-gradient-secondary gradient-text-fallback",
            descriptionClassName
          )}
        >
          {description}
        </p>
      )}
    </div>
  );
};
