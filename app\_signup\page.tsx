import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormField, FormLabel } from "@/components/ui/form";

export default function SignUpPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center px-6">
      <div className="max-w-md w-full">
        <div className="bg-card rounded-3xl shadow-strong p-8 border border-border/50 backdrop-blur-sm animate-scale-in">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg interactive">
              <span className="text-primary-foreground font-bold text-2xl">
                A
              </span>
            </div>
            <h1 className="text-3xl font-bold text-card-foreground mb-3">
              Create Your AvencaLink
            </h1>
            <p className="text-muted-foreground">
              Join thousands of creators sharing their content
            </p>
          </div>

          <Form className="space-y-6">
            <FormField>
              <FormLabel htmlFor="username">Choose your username</FormLabel>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm z-10">
                  avencalink.com/
                </span>
                <Input
                  type="text"
                  id="username"
                  className="pl-32"
                  placeholder="yourname"
                />
              </div>
            </FormField>

            <FormField>
              <Input
                type="email"
                id="email"
                label="Email address"
                placeholder="<EMAIL>"
              />
            </FormField>

            <FormField>
              <Input
                type="password"
                id="password"
                label="Password"
                placeholder="••••••••"
                description="Must be at least 8 characters long"
              />
            </FormField>

            <Button className="w-full" size="lg" animation="lift">
              Create Account
            </Button>

            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Already have an account?{" "}
                <Link
                  href="/login"
                  className="text-primary hover:text-primary/80 font-medium transition-colors"
                >
                  Sign in
                </Link>
              </p>
            </div>
          </Form>
        </div>

        <div className="text-center mt-8">
          <Link
            href="/"
            className="text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  );
}
