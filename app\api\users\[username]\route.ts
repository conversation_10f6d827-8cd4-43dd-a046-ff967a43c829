import { NextRequest, NextResponse } from 'next/server'
import { UserProfileResponse } from '@/types/user'
import { validateUsername } from '@/lib/utils'

const API_BASE_URL = process.env.NEXT_PUBLIC_FIREBASE_API_BASE_URL || 'https://cardlink-bio-default-rtdb.firebaseio.com/users'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> }
): Promise<NextResponse<UserProfileResponse>> {
  try {
    const { username } = await params

    // Validate username format
    if (!validateUsername(username)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid username format',
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'User not found',
      },
      { status: 404 }
    )
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> }
): Promise<NextResponse> {
  try {
    const { username } = await params

    // Validate username format
    if (!validateUsername(username)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid username format',
        },
        { status: 400 }
      )
    }

    // Check if user exists first
    const checkResponse = await fetch(`${API_BASE_URL}/${username}.json`)
    if (!checkResponse.ok) {
      if (checkResponse.status === 404) {
        return NextResponse.json(
          {
            success: false,
            error: 'User not found',
          },
          { status: 404 }
        )
      }
      throw new Error(`Failed to check user existence: ${checkResponse.status}`)
    }

    const userData = await checkResponse.json()
    if (!userData) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found',
        },
        { status: 404 }
      )
    }

    // Delete the user from Firebase
    const deleteResponse = await fetch(`${API_BASE_URL}/${username}.json`, {
      method: 'DELETE',
    })

    if (!deleteResponse.ok) {
      throw new Error(`Failed to delete user: ${deleteResponse.status}`)
    }

    return NextResponse.json({
      success: true,
      message: `User ${username} and all associated data have been permanently deleted`,
      deletedUser: {
        username,
        displayName: userData.user?.name || username,
        recordCount: calculateRecordCount(userData),
      },
    })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    )
  }
}

// Helper function to calculate record count
function calculateRecordCount(userData: Record<string, unknown>): number {
  if (!userData) return 0

  let count = 0
  const sections = [
    'featuresSection',
    'gallery',
    'servicesSection',
    'reviews',
    'team',
    'links',
    'socialMedia',
  ]

  sections.forEach((section) => {
    const sectionData = userData[section] as Record<string, unknown> | unknown[]
    if (sectionData && typeof sectionData === 'object' && 'items' in sectionData) {
      const items = (sectionData as { items: unknown[] }).items
      if (Array.isArray(items)) {
        count += items.length
      }
    } else if (Array.isArray(sectionData)) {
      count += sectionData.length
    }
  })

  return count
}
