"use client";

import React, { useState, use<PERSON><PERSON>back, useMemo, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ThemeToggle } from "@/components/theme-toggle";
import SectionManager from "@/components/admin/SectionManager";
import UserSelector from "@/components/admin/UserSelector";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Skeleton } from "@/components/ui/skeleton";
import { useNotification } from "./useNotification";
import { NotificationContainer } from "./NotificationContainer";
import {
  Settings,
  Users,
  Image,
  Star,
  Link,
  MapPin,
  Palette,
  Search,
  Filter,
  Download,
  Upload,
  BarChart,
  Clock,
  Database,
  Globe,
  Sparkles,
  Shield,
  Activity,
} from "lucide-react";

// Data section types for navigation with enhanced descriptions and colors
const dataSections = [
  {
    id: "featuresSection",
    name: "Features",
    icon: Sparkles,
    description: "Showcase key features and highlights",
    color: "from-purple-500 to-pink-500",
    bgColor: "bg-purple-50 dark:bg-purple-900/20",
    count: 0,
  },
  {
    id: "gallery",
    name: "Gallery",
    icon: Image,
    description: "Visual portfolio and media collection",
    color: "from-blue-500 to-cyan-500",
    bgColor: "bg-blue-50 dark:bg-blue-900/20",
    count: 0,
  },
  {
    id: "servicesSection",
    name: "Services",
    icon: Settings,
    description: "Service offerings and packages",
    color: "from-green-500 to-emerald-500",
    bgColor: "bg-green-50 dark:bg-green-900/20",
    count: 0,
  },
  {
    id: "reviews",
    name: "Reviews",
    icon: Star,
    description: "Customer testimonials and feedback",
    color: "from-yellow-500 to-orange-500",
    bgColor: "bg-yellow-50 dark:bg-yellow-900/20",
    count: 0,
  },
  {
    id: "team",
    name: "Team",
    icon: Users,
    description: "Team members and staff profiles",
    color: "from-indigo-500 to-purple-500",
    bgColor: "bg-indigo-50 dark:bg-indigo-900/20",
    count: 0,
  },
  {
    id: "links",
    name: "Links",
    icon: Link,
    description: "Important links and navigation",
    color: "from-teal-500 to-green-500",
    bgColor: "bg-teal-50 dark:bg-teal-900/20",
    count: 0,
  },
  {
    id: "socialMedia",
    name: "Social Media",
    icon: Globe,
    description: "Social media profiles and links",
    color: "from-pink-500 to-rose-500",
    bgColor: "bg-pink-50 dark:bg-pink-900/20",
    count: 0,
  },
  {
    id: "location",
    name: "Location",
    icon: MapPin,
    description: "Address and contact information",
    color: "from-red-500 to-pink-500",
    bgColor: "bg-red-50 dark:bg-red-900/20",
    count: 0,
  },
  {
    id: "settings",
    name: "Settings",
    icon: Palette,
    description: "Site configuration and appearance",
    color: "from-gray-500 to-slate-500",
    bgColor: "bg-gray-50 dark:bg-gray-900/20",
    count: 0,
  },
];

export default function AdminDashboard() {
  const [activeSection, setActiveSection] = useState<string>("featuresSection");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [showUserSelector, setShowUserSelector] = useState(true);
  const [isLoading] = useState(false);
  const { notifications, showNotification, removeNotification } =
    useNotification();

  const handleSectionChange = useCallback(
    (sectionId: string) => {
      setActiveSection(sectionId);
      setSearchQuery(""); // Clear search when switching sections
      const section = dataSections.find((s) => s.id === sectionId);
      showNotification(
        "info",
        `Switched to ${section?.name || "unknown"} section`
      );
    },
    [showNotification]
  );

  const handleUserSelect = useCallback(
    (username: string) => {
      setSelectedUser(username);
      setShowUserSelector(false);
      showNotification("success", `Selected user: @${username}`);
    },
    [showNotification]
  );

  const handleChangeUser = useCallback(() => {
    setShowUserSelector(true);
    setSelectedUser(null);
  }, []);

  // Memoize active section info
  const activeSectionInfo = useMemo(
    () => dataSections.find((s) => s.id === activeSection),
    [activeSection]
  );

  // Keyboard navigation for sections
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        const index = dataSections.findIndex((s) => s.id === activeSection);
        if (e.key === "ArrowUp" && index > 0) {
          e.preventDefault();
          handleSectionChange(dataSections[index - 1].id);
        } else if (e.key === "ArrowDown" && index < dataSections.length - 1) {
          e.preventDefault();
          handleSectionChange(dataSections[index + 1].id);
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [activeSection, handleSectionChange]);

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-background">
        {/* Enhanced Header */}
        <header className="border-b bg-gradient-to-r from-card to-card/80 shadow-sm backdrop-blur-sm">
          <div className="container mx-auto px-4 py-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center shadow-lg">
                    <Shield className="h-6 w-6 text-primary-foreground" />
                  </div>
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
                    Admin Dashboard
                    <Badge variant="secondary" className="text-xs font-normal">
                      v2.0
                    </Badge>
                  </h1>
                  <div className="flex items-center gap-2 mt-1">
                    <p className="text-sm text-muted-foreground">
                      {selectedUser
                        ? `Managing data for @${selectedUser}`
                        : "Select a user to manage their data"}
                    </p>
                    {selectedUser && (
                      <Badge variant="outline" className="text-xs">
                        <Activity className="h-3 w-3 mr-1" />
                        Active
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-2 sm:gap-3">
                <div className="flex items-center gap-2 px-3 py-1.5 bg-muted/50 rounded-lg"></div>
                <ThemeToggle variant="icon-only" />
                {selectedUser && (
                  <>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          aria-label="Export user data"
                          className="hover:bg-green-50 hover:border-green-200 dark:hover:bg-green-900/20"
                          onClick={() => {}}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          <span className="hidden sm:inline">Export</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Export all data as JSON</p>
                      </TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          aria-label="Import user data"
                          className="hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-blue-900/20"
                          onClick={() => {
                            showNotification(
                              "warning",
                              "Import feature coming soon!"
                            );
                          }}
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          <span className="hidden sm:inline">Import</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Import data from JSON file</p>
                      </TooltipContent>
                    </Tooltip>
                  </>
                )}
                <Button
                  variant={selectedUser ? "outline" : "default"}
                  size="sm"
                  onClick={selectedUser ? handleChangeUser : undefined}
                  aria-label={selectedUser ? "Change user" : "Select user"}
                  className={
                    selectedUser
                      ? "hover:bg-orange-50 hover:border-orange-200 dark:hover:bg-orange-900/20"
                      : ""
                  }
                >
                  <Users className="h-4 w-4 mr-2" />
                  {selectedUser ? "Change User" : "Select User"}
                </Button>
              </div>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Sidebar Navigation */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Data Sections</CardTitle>
                  <p className="text-xs text-muted-foreground mt-1">
                    Use Ctrl/Cmd + ↑↓ to navigate
                  </p>
                </CardHeader>
                <CardContent className="p-0">
                  <nav className="space-y-1">
                    {dataSections.map((section) => {
                      const Icon = section.icon;
                      const isActive = activeSection === section.id;

                      return (
                        <button
                          key={section.id}
                          onClick={() => handleSectionChange(section.id)}
                          className={`w-full flex items-center gap-3 px-4 py-3 text-left transition-all duration-200 hover:bg-accent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary rounded-lg ${
                            isActive
                              ? "bg-accent text-accent-foreground border-l-4 border-primary shadow-sm"
                              : "text-muted-foreground hover:text-foreground"
                          }`}
                          aria-label={`Navigate to ${section.name} section`}
                          aria-current={isActive ? "page" : undefined}
                        >
                          <Icon
                            className={`h-4 w-4 transition-transform ${
                              isActive ? "scale-110" : ""
                            }`}
                          />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate">
                              {section.name}
                            </div>
                            <div className="text-xs text-muted-foreground truncate">
                              {section.description}
                            </div>
                          </div>
                          {isActive && (
                            <div className="w-1.5 h-1.5 bg-primary rounded-full animate-pulse" />
                          )}
                        </button>
                      );
                    })}
                  </nav>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <BarChart className="h-4 w-4" />
                    Quick Stats
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {isLoading ? (
                    <>
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                    </>
                  ) : (
                    <>
                      <div className="flex justify-between items-center p-2 rounded-lg hover:bg-accent/50 transition-colors">
                        <span className="text-sm text-muted-foreground flex items-center gap-2">
                          <Database className="h-3 w-3" />
                          Total Records
                        </span>
                        <Badge variant="secondary" className="font-mono">
                          156
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center p-2 rounded-lg hover:bg-accent/50 transition-colors">
                        <span className="text-sm text-muted-foreground flex items-center gap-2">
                          <Clock className="h-3 w-3" />
                          Last Updated
                        </span>
                        <Badge variant="outline">2 hours ago</Badge>
                      </div>
                      <div className="flex justify-between items-center p-2 rounded-lg hover:bg-accent/50 transition-colors">
                        <span className="text-sm text-muted-foreground flex items-center gap-2">
                          <Database className="h-3 w-3" />
                          Data Size
                        </span>
                        <Badge variant="outline" className="font-mono">
                          2.4 MB
                        </Badge>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Main Content Area */}
            <div className="lg:col-span-3">
              {showUserSelector || !selectedUser ? (
                <UserSelector
                  selectedUser={selectedUser}
                  onUserSelect={handleUserSelect}
                  onUserChange={setSelectedUser}
                />
              ) : (
                <>
                  {/* Search and Filter Bar */}
                  <Card className="mb-6 shadow-sm">
                    <CardContent className="p-4">
                      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
                        <div className="flex-1 relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
                          <Input
                            placeholder={`Search ${
                              activeSectionInfo?.name.toLowerCase() || "records"
                            }...`}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-10 pr-4 h-10"
                            aria-label={`Search ${
                              activeSectionInfo?.name || "records"
                            }`}
                            autoComplete="off"
                          />
                        </div>
                        <Button
                          variant="outline"
                          size="default"
                          className="sm:w-auto w-full"
                          aria-label="Open filter options"
                        >
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Dynamic Content Area */}
                  <div className="animate-in fade-in-50 duration-500">
                    <SectionManager
                      sectionId={activeSection}
                      username={selectedUser}
                      searchQuery={searchQuery}
                      onDataChange={() => {
                        console.log("Data changed for section:", activeSection);
                        showNotification(
                          "success",
                          "Changes saved successfully!"
                        );
                      }}
                    />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
        <NotificationContainer
          notifications={notifications}
          onRemove={removeNotification}
        />
      </div>
    </TooltipProvider>
  );
}
