"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormField,
  FormLabel,
  FormMessage,
  FormActions,
} from "@/components/ui/form";
import {
  <PERSON>dal,
  ModalContent,
  ModalHeader,
  ModalTitle,
  ModalDescription,
} from "@/components/ui/modal";
import { DataField } from "@/services/adminDataService";
import { Save, X } from "lucide-react";

export interface DynamicFormProps {
  fields: DataField[];
  initialData?: any;
  onSubmit: (data: any) => Promise<{ success: boolean; error?: string }>;
  onCancel?: () => void;
  title?: string;
  description?: string;
  submitLabel?: string;
  cancelLabel?: string;
  isModal?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

interface FormState {
  [key: string]: {
    value: any;
    error?: string;
    touched: boolean;
  };
}

export default function DynamicForm({
  fields,
  initialData = {},
  onSubmit,
  onCancel,
  title = "Form",
  description,
  submitLabel = "Save",
  cancelLabel = "Cancel",
  isModal = false,
  open = true,
  onOpenChange,
}: DynamicFormProps) {
  const [formState, setFormState] = useState<FormState>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Helper function to get nested property value
  const getNestedValue = (obj: any, path: string) => {
    if (!obj || !path) return undefined;
    return path.split(".").reduce((current, key) => current?.[key], obj);
  };

  // Initialize form state
  useEffect(() => {
    const initialState: FormState = {};
    fields.forEach((field) => {
      const nestedValue = getNestedValue(initialData, field.key);
      initialState[field.key] = {
        value: nestedValue ?? getDefaultValue(field.type),
        touched: false,
      };
    });
    setFormState(initialState);
  }, [fields, initialData]);

  const getDefaultValue = (type: string) => {
    switch (type) {
      case "boolean":
        return false;
      case "number":
        return 0;
      case "select":
        return "";
      default:
        return "";
    }
  };

  const updateField = (key: string, value: any) => {
    setFormState((prev) => ({
      ...prev,
      [key]: {
        ...prev[key],
        value,
        touched: true,
        error: validateField(key, value),
      },
    }));
  };

  const validateField = (key: string, value: any): string | undefined => {
    const field = fields.find((f) => f.key === key);
    if (!field) return undefined;

    // Required validation
    if (
      field.required &&
      (value === undefined || value === null || value === "")
    ) {
      return `${field.label} is required`;
    }

    // Type validation
    if (value !== undefined && value !== null && value !== "") {
      switch (field.type) {
        case "number":
          if (isNaN(Number(value))) {
            return `${field.label} must be a number`;
          }
          break;
        case "url":
          try {
            new URL(value);
          } catch {
            return `${field.label} must be a valid URL`;
          }
          break;
        case "email":
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            return `${field.label} must be a valid email`;
          }
          break;
      }

      // Custom validation
      if (field.validation) {
        const validationError = field.validation(value);
        if (validationError) {
          return validationError;
        }
      }
    }

    return undefined;
  };

  const validateForm = (): boolean => {
    let isValid = true;
    const newFormState = { ...formState };

    fields.forEach((field) => {
      const error = validateField(field.key, formState[field.key]?.value);
      if (error) {
        isValid = false;
        newFormState[field.key] = {
          ...newFormState[field.key],
          error,
          touched: true,
        };
      }
    });

    setFormState(newFormState);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const formData: any = {};
      Object.entries(formState).forEach(([key, field]) => {
        formData[key] = field.value;
      });

      const result = await onSubmit(formData);

      if (result.success) {
        if (isModal && onOpenChange) {
          onOpenChange(false);
        }
      } else {
        setSubmitError(result.error || "An error occurred");
      }
    } catch (error) {
      setSubmitError(
        error instanceof Error ? error.message : "An unexpected error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (isModal && onOpenChange) {
      onOpenChange(false);
    } else if (onCancel) {
      onCancel();
    }
  };

  const renderField = (field: DataField) => {
    const fieldState = formState[field.key];
    if (!fieldState) return null;

    const { value, error, touched } = fieldState;

    switch (field.type) {
      case "boolean":
        return (
          <FormField key={field.key}>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id={field.key}
                checked={value}
                onChange={(e) => updateField(field.key, e.target.checked)}
                className="rounded border-gray-300"
              />
              <FormLabel htmlFor={field.key} required={field.required}>
                {field.label}
              </FormLabel>
            </div>
            {touched && error && <FormMessage>{error}</FormMessage>}
          </FormField>
        );

      case "textarea":
        return (
          <FormField key={field.key}>
            <FormLabel htmlFor={field.key} required={field.required}>
              {field.label}
            </FormLabel>
            <textarea
              id={field.key}
              value={value}
              onChange={(e) => updateField(field.key, e.target.value)}
              className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              rows={3}
            />
            {touched && error && <FormMessage>{error}</FormMessage>}
          </FormField>
        );

      case "select":
        return (
          <FormField key={field.key}>
            <FormLabel htmlFor={field.key} required={field.required}>
              {field.label}
            </FormLabel>
            <select
              id={field.key}
              value={value}
              onChange={(e) => updateField(field.key, e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <option value="">Select {field.label}</option>
              {field.options?.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
            {touched && error && <FormMessage>{error}</FormMessage>}
          </FormField>
        );

      default:
        // Check if this is a color field based on the field key or label
        const isColorField =
          field.key.includes("color") ||
          field.label.toLowerCase().includes("color");

        return (
          <FormField key={field.key}>
            <FormLabel htmlFor={field.key} required={field.required}>
              {field.label}
            </FormLabel>
            {isColorField ? (
              <div className="flex gap-2">
                <Input
                  id={field.key}
                  type="color"
                  value={value || "#000000"}
                  onChange={(e) => updateField(field.key, e.target.value)}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  type="text"
                  value={value || ""}
                  onChange={(e) => updateField(field.key, e.target.value)}
                  placeholder="#000000"
                  error={touched && error ? error : undefined}
                  className="flex-1"
                />
              </div>
            ) : (
              <Input
                id={field.key}
                type={
                  field.type === "number"
                    ? "number"
                    : field.type === "email"
                    ? "email"
                    : "text"
                }
                value={value}
                onChange={(e) => updateField(field.key, e.target.value)}
                error={touched && error ? error : undefined}
              />
            )}
            {touched && error && <FormMessage>{error}</FormMessage>}
          </FormField>
        );
    }
  };

  const formContent = (
    <Form onSubmit={handleSubmit}>
      <div className="space-y-4">{fields.map(renderField)}</div>

      {submitError && <FormMessage type="error">{submitError}</FormMessage>}

      <FormActions>
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isSubmitting}
        >
          <X className="h-4 w-4 mr-2" />
          {cancelLabel}
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          isLoading={isSubmitting}
          loadingText="Saving..."
        >
          <Save className="h-4 w-4 mr-2" />
          {submitLabel}
        </Button>
      </FormActions>
    </Form>
  );

  if (isModal) {
    return (
      <Modal open={open} onOpenChange={onOpenChange}>
        <ModalContent size="lg">
          <ModalHeader>
            <ModalTitle>{title}</ModalTitle>
            {description && <ModalDescription>{description}</ModalDescription>}
          </ModalHeader>
          {formContent}
        </ModalContent>
      </Modal>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </CardHeader>
      <CardContent>{formContent}</CardContent>
    </Card>
  );
}

// Convenience component for item forms
export interface ItemFormProps {
  sectionId: string;
  fields: DataField[];
  initialData?: any;
  onSubmit: (data: any) => Promise<{ success: boolean; error?: string }>;
  onCancel?: () => void;
  isModal?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ItemForm({
  sectionId,
  fields,
  initialData,
  onSubmit,
  onCancel,
  isModal = true,
  open = true,
  onOpenChange,
}: ItemFormProps) {
  const isEditing = !!initialData?.id;

  return (
    <DynamicForm
      fields={fields}
      initialData={initialData}
      onSubmit={onSubmit}
      onCancel={onCancel}
      title={`${isEditing ? "Edit" : "Add"} ${sectionId
        .replace(/([A-Z])/g, " $1")
        .replace(/^./, (str) => str.toUpperCase())}`}
      description={`${
        isEditing ? "Update" : "Create"
      } a new item in the ${sectionId} section`}
      submitLabel={isEditing ? "Update" : "Create"}
      isModal={isModal}
      open={open}
      onOpenChange={onOpenChange}
    />
  );
}
