import React from "react";
import Image from "next/image";
import { useLinkNavigation } from "@/components/hooks/useLinkNavigation";
import { SafeExternalLink } from "@/components/security/SafeExternalLink";

interface ArtistProfileProps {
  imageUrl: string;
  name: string;
  specialty: string;
  url?: string;
}

export const ArtistProfile: React.FC<ArtistProfileProps> = ({
  imageUrl,
  name,
  specialty,
  url,
}) => {
  const { handleLinkClick } = useLinkNavigation();

  const content = (
    <div className="flex flex-col items-center gap-4 transition-transform duration-300 hover:scale-105">
      <div className="w-40 h-40 rounded-full border-6 border-gray-400 hover:border-gray-400 transition-colors duration-300 overflow-hidden relative">
        <Image
          src={imageUrl}
          alt={`${name}, ${specialty}`}
          fill
          sizes="160px"
          className="object-cover object-center"
          priority={false}
        />
      </div>
      <div className="md:w-[340px] w-full max-w-sm sm:max-w-xs text-center">
        <p className="text-white text-xl font-bold text-center">{name}</p>
        <p className="text-gray-400">{specialty}</p>
      </div>
    </div>
  );

  if (url) {
    return (
      <SafeExternalLink
        href={url}
        onSafeClick={(e, safeUrl) => handleLinkClick(safeUrl, e)}
        className="block cursor-pointer"
        aria-label={`Visitar perfil de ${name}`}
        target="_blank"
        rel="noopener noreferrer"
      >
        {content}
      </SafeExternalLink>
    );
  }

  return content;
};
