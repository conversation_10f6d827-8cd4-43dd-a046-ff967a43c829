import { UserProfile, validateUserProfile } from '@/types'
import { dataSections, itemFieldDefinitions } from './admin/sectionDefinitions'
import { generateNewId, findItemById, safeJsonParse } from './admin/utils'
import { <PERSON><PERSON>Result, ItemData } from './admin/types'
import { SUCCESS_MESSAGES, ERROR_MESSAGES, DEFAULT_API_BASE_URL } from './admin/constants'

class AdminDataService {
  private dataCache = new Map<string, UserProfile>()
  private readonly API_BASE_URL = process.env.NEXT_PUBLIC_FIREBASE_API_BASE_URL || DEFAULT_API_BASE_URL

  /**
   * Get all available usernames/profiles from Firebase
   */
  async getAvailableProfiles(): Promise<string[]> {
    try {
      const response = await fetch(`${this.API_BASE_URL}.json`)
      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.status}`)
      }

      // Safely parse JSON response
      const data = await safeJsonParse(response);
      if (!data) {
        return []
      }

      // Return the keys (usernames) from the Firebase response
      return Object.keys(data)
    } catch (error) {
      console.error('Error fetching available profiles:', error)
      return []
    }
  }

  /**
   * Load user profile data from Firebase
   */
  async loadUserData(username: string): Promise<UserProfile | null> {
    try {
      // Check cache first
      const cached = this.dataCache.get(username)
      if (cached) {
        return cached
      }

      const response = await fetch(`${this.API_BASE_URL}/${username}.json`)
      if (!response.ok) {
        if (response.status === 404) {
          return null
        }
        throw new Error(`Failed to load data: ${response.status}`)
      }

      const data = await safeJsonParse(response);
      if (!data) {
        return null
      }

      // Ensure data is a valid object before validation
      if (typeof data !== 'object' || Array.isArray(data)) {
        console.warn('Invalid data format received from API:', typeof data)
        return null
      }

      const validationResult = validateUserProfile(data as UserProfile)

      if (!validationResult.isValid) {
        console.warn('Data validation warnings:', validationResult.errors)
      }

      // Cache the data
      this.dataCache.set(username, data as UserProfile)

      return data as UserProfile
    } catch (error) {
      console.error('Error loading user data:', error)
      return null
    }
  }

  /**
   * Save user profile data to Firebase
   */
  async saveUserData(username: string, data: UserProfile): Promise<CrudResult> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/${username}.json`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`Failed to save data: ${response.status}`)
      }

      // Update cache
      this.dataCache.set(username, data)

      return { success: true, message: SUCCESS_MESSAGES.DATA_SAVED }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save data'
      }
    }
  }

  /**
   * Get data for a specific section
   */
  async getSectionData(username: string, sectionId: string): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: ERROR_MESSAGES.USER_NOT_FOUND }
      }

      const sectionData = (userData as unknown as Record<string, unknown>)[sectionId]
      if (sectionData === undefined) {
        return { success: false, error: ERROR_MESSAGES.SECTION_NOT_FOUND }
      }

      return { success: true, data: sectionData }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get items from an array-type section
   */
  async getSectionItems(username: string, sectionId: string): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: ERROR_MESSAGES.USER_NOT_FOUND }
      }

      // Find the section configuration
      const sectionConfig = dataSections.find(section => section.id === sectionId)

      // For sections that are direct arrays in the root (like links, socialMedia)
      if (sectionConfig && !sectionConfig.itemsKey) {
        const items = (userData as unknown as Record<string, unknown>)[sectionId]

        // Ensure we return an array
        if (Array.isArray(items)) {
          return { success: true, data: items }
        } else if (items === null || items === undefined) {
          return { success: true, data: [] }
        } else {
          // If it's not an array, wrap it or return empty array
          console.warn(`Section ${sectionId} is not an array:`, items)
          return { success: true, data: [] }
        }
      }

      // For sections that have nested items (like featuresSection.items)
      const section = (userData as unknown as Record<string, unknown>)[sectionId]
      if (!section) {
        return { success: false, error: ERROR_MESSAGES.SECTION_NOT_FOUND }
      }

      const itemsKey = sectionConfig?.itemsKey || 'items'
      const items = (section as Record<string, unknown>)[itemsKey] || []
      return { success: true, data: items }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create a new item in a section
   */
  async createItem(username: string, sectionId: string, itemData: ItemData): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: ERROR_MESSAGES.USER_NOT_FOUND }
      }

      // Find the section configuration
      const sectionConfig = dataSections.find(s => s.id === sectionId)

      // For sections that are direct arrays (like links, socialMedia)
      if (sectionConfig && !sectionConfig.itemsKey) {
        let items = (userData as unknown as Record<string, unknown>)[sectionId]

        // Ensure we have an array to work with
        if (!Array.isArray(items)) {
          items = []
        }

        // Generate new ID if needed
        if (itemData.id === undefined) {
          itemData.id = generateNewId(items as ItemData[])
        }

        (items as ItemData[]).push(itemData);
        (userData as unknown as Record<string, unknown>)[sectionId] = items
      } else {
        // For sections with nested items (like featuresSection.items, team.members)
        let section = (userData as unknown as Record<string, unknown>)[sectionId]
        if (!section || typeof section !== 'object') {
          section = {}
            ; (userData as unknown as Record<string, unknown>)[sectionId] = section
        }

        const itemsKey = sectionConfig?.itemsKey || 'items'
        let items = (section as Record<string, unknown>)[itemsKey]

        if (!Array.isArray(items)) {
          items = []
            ; (section as Record<string, unknown>)[itemsKey] = items
        }

        // Generate new ID if needed
        if (itemData.id === undefined) {
          itemData.id = generateNewId(items as ItemData[])
        }

        (items as ItemData[]).push(itemData)
      }

      // Save to Firebase
      const saveResult = await this.saveUserData(username, userData)
      if (!saveResult.success) {
        return saveResult
      }

      return {
        success: true,
        data: itemData,
        message: SUCCESS_MESSAGES.ITEM_CREATED
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Update an existing item
   */
  async updateItem(username: string, sectionId: string, itemId: string | number, itemData: Partial<ItemData>): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: ERROR_MESSAGES.USER_NOT_FOUND }
      }

      const sectionConfig = dataSections.find(s => s.id === sectionId)
      let items: ItemData[]

      // For sections that are direct arrays (like links, socialMedia)
      if (sectionConfig && !sectionConfig.itemsKey) {
        const sectionData = (userData as unknown as Record<string, unknown>)[sectionId]
        if (!Array.isArray(sectionData)) {
          return { success: false, error: ERROR_MESSAGES.SECTION_NOT_FOUND }
        }
        items = sectionData
      } else {
        // For sections with nested items
        const section = (userData as unknown as Record<string, unknown>)[sectionId]
        if (!section || typeof section !== 'object') {
          return { success: false, error: ERROR_MESSAGES.SECTION_NOT_FOUND }
        }
        const itemsKey = sectionConfig?.itemsKey || 'items'
        items = (section as Record<string, unknown>)[itemsKey] as ItemData[]
        if (!Array.isArray(items)) {
          return { success: false, error: ERROR_MESSAGES.ITEMS_ARRAY_NOT_FOUND }
        }
      }

      const { index } = findItemById(items, itemId)
      if (index === -1) {
        return { success: false, error: ERROR_MESSAGES.ITEM_NOT_FOUND }
      }

      // Update the item data
      items[index] = { ...items[index], ...itemData }

      // Save to Firebase
      const saveResult = await this.saveUserData(username, userData)
      if (!saveResult.success) {
        return saveResult
      }

      return {
        success: true,
        data: items[index],
        message: SUCCESS_MESSAGES.ITEM_UPDATED
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Delete an item
   */
  async deleteItem(username: string, sectionId: string, itemId: string | number): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: ERROR_MESSAGES.USER_NOT_FOUND }
      }

      const sectionConfig = dataSections.find(s => s.id === sectionId)
      let items: ItemData[]

      // For sections that are direct arrays (like links, socialMedia)
      if (sectionConfig && !sectionConfig.itemsKey) {
        const sectionData = (userData as unknown as Record<string, unknown>)[sectionId]
        if (!Array.isArray(sectionData)) {
          return { success: false, error: ERROR_MESSAGES.SECTION_NOT_FOUND }
        }
        items = sectionData
      } else {
        // For sections with nested items
        const section = (userData as unknown as Record<string, unknown>)[sectionId]
        if (!section || typeof section !== 'object') {
          return { success: false, error: ERROR_MESSAGES.SECTION_NOT_FOUND }
        }
        const itemsKey = sectionConfig?.itemsKey || 'items'
        items = (section as Record<string, unknown>)[itemsKey] as ItemData[]
        if (!Array.isArray(items)) {
          console.warn(`Items array ${itemsKey} not found in section ${sectionId} for user ${username}`);
          return { success: false, error: ERROR_MESSAGES.ITEMS_ARRAY_NOT_FOUND }
        }
      }

      const { item, index } = findItemById(items, itemId)
      if (!item || index === -1) {
        console.warn(`Failed to find item with ID ${itemId} in section ${sectionId} for user ${username}`);
        return { success: false, error: ERROR_MESSAGES.ITEM_NOT_FOUND }
      }

      // Remove the item
      const deletedItem = items.splice(index, 1)[0]
      console.info(`Item with ID ${itemId} deleted from section ${sectionId} for user ${username}`);

      // Save to Firebase
      const saveResult = await this.saveUserData(username, userData)
      if (!saveResult.success) {
        return saveResult
      }

      return {
        success: true,
        data: deletedItem,
        message: SUCCESS_MESSAGES.ITEM_DELETED
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Validate item data against field definitions
   */
  validateItemData(sectionId: string, itemData: ItemData): { isValid: boolean; errors: string[] } {
    const fields = itemFieldDefinitions[sectionId] || []
    const errors: string[] = []

    for (const field of fields) {
      const value = itemData[field.key]

      // Check required fields
      if (field.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field.label} is required`)
        continue
      }

      // Type validation
      if (value !== undefined && value !== null && value !== '') {
        switch (field.type) {
          case 'number':
            if (isNaN(Number(value))) {
              errors.push(`${field.label} must be a number`)
            }
            break
          case 'url':
            try {
              new URL(String(value))
            } catch {
              errors.push(`${field.label} must be a valid URL`)
            }
            break
          case 'email':
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(String(value))) {
              errors.push(`${field.label} must be a valid email`)
            }
            break
        }

        // Custom validation
        if (field.validation) {
          const validationError = field.validation(value)
          if (validationError) {
            errors.push(validationError)
          }
        }
      }
    }

    return { isValid: errors.length === 0, errors }
  }
}

export const adminDataService = new AdminDataService()

// Re-export types and definitions for convenience
export * from './admin/types'
export { dataSections, itemFieldDefinitions } from './admin/sectionDefinitions'
