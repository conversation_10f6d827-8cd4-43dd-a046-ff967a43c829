"use client";

import React from "react";
import { Toaster } from "@/components/ui/toaster";
import {
  LinktreeExtractorForm,
  ApiResponseDisplay,
  useLinktreeExtractor,
} from "@/components/linktree-extractor";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";

export default function LinktreeExtractorPage() {
  const { formData, apiResponse, isLoading } = useLinktreeExtractor();

  const showEmptyState = !apiResponse && !isLoading;

  return (
    <main
      className="container mx-auto p-4 sm:p-6 md:p-8"
      role="main"
      aria-labelledby="ltx-heading"
    >
      <Toaster />
      {/* Loading Indicator */}
      {isLoading ? (
        <div className="mb-6 sm:mb-8" aria-live="polite" aria-busy="true">
          <Progress value={66} className="h-2" />
          <p className="mt-2 text-xs text-muted-foreground">
            Processando a extração e formatando os dados…
          </p>
        </div>
      ) : (
        <Separator className="mb-6 sm:mb-8" />
      )}

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-start">
        {/* Form Side */}
        <LinktreeExtractorForm />

        {/* Preview / Output Side */}
        <div className="lg:sticky lg:top-6">
          {isLoading ? (
            <div className="space-y-4" aria-hidden="true">
              <Skeleton className="h-5 w-40" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-[90%]" />
              <Skeleton className="h-4 w-[85%]" />
              <Skeleton className="h-4 w-[80%]" />
            </div>
          ) : showEmptyState ? (
            <div className="text-center py-12 text-muted-foreground">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-muted flex items-center justify-center">
                <span className="text-xl" aria-hidden="true">
                  🔎
                </span>
              </div>
              <p className="font-medium">Aguardando dados</p>
              <p className="text-sm">
                Preencha o formulário à esquerda para visualizar o resultado
                aqui.
              </p>
            </div>
          ) : (
            <ApiResponseDisplay
              apiResponse={apiResponse}
              isLoading={isLoading}
              selectedTemplate={formData.selectedTemplate}
            />
          )}
        </div>
      </div>
    </main>
  );
}
