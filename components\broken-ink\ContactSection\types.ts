import { UserProfile, ColorSettings } from "@/types/user";
export interface ContactData {
    links: Array<{
        text: string;
        url: string;
        classIcon?: string;
    }>;
    socialMedia: Array<{
        text: string;
        url: string;
        classIcon?: string;
    }>;
}

export interface LocationData {
    enabled: boolean;
    title: string;
    description: string;
    address: string | null;
    contact: {
        phone: string;
        whatsapp: string;
    } | null;
    hours: {
        weekdays: string;
        weekends: string;
        closed?: string;
    } | null;
    googleMapsUrl: string | null;
    colors: ColorSettings;
}
export interface ContactSectionProps {
    profile: UserProfile;
}