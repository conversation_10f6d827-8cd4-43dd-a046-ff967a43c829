"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Download, Send, Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";

interface ActionButtonsProps {
  onExtract: () => void;
  onSubmit: () => void;
  isLoading: boolean;
  canExtract: boolean;
  disabled?: boolean;
}

export default function ActionButtons({
  onExtract,
  onSubmit,
  isLoading,
  canExtract,
  disabled = false,
}: ActionButtonsProps) {
  return (
    <div className="flex flex-col sm:flex-row gap-3 pt-6">
      <Button
        onClick={onExtract}
        disabled={isLoading || !canExtract || disabled}
        variant="outline"
        size="lg"
        className={cn(
          "flex-1 h-12 transition-all duration-200",
          "hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700",
          "dark:hover:bg-blue-950/50 dark:hover:border-blue-700",
          "focus:ring-2 focus:ring-blue-500/20",
          "rounded-2xl",
          !canExtract && "opacity-50 cursor-not-allowed"
        )}
        type="button"
      >
        {isLoading ? (
          <LoadingSpinner size="sm" className="mr-2" />
        ) : (
          <Download className="w-4 h-4 mr-2" />
        )}
        {isLoading ? "Extracting..." : "Extract Info"}
      </Button>

      <Button
        onClick={onSubmit}
        disabled={isLoading || disabled}
        size="lg"
        className={cn(
          "flex-1 h-12 transition-all duration-200",
          "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",
          "focus:ring-2 focus:ring-purple-500/20",
          "shadow-lg hover:shadow-xl",
          "transform hover:scale-[1.02] active:scale-[0.98] rounded-2xl"
        )}
        type="button"
      >
        {isLoading ? (
          <LoadingSpinner size="sm" className="mr-2" />
        ) : (
          <div className="flex items-center gap-2">
            <Send className="w-4 h-4" />
          </div>
        )}
        {isLoading ? "Processing..." : "Process & Submit"}
      </Button>
    </div>
  );
}
