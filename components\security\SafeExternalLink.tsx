import React from "react";

export type SafeExternalLinkProps =
  React.AnchorHTMLAttributes<HTMLAnchorElement> & {
    href: string | undefined;
    onSafeClick?: (
      e: React.MouseEvent<HTMLAnchorElement>,
      safeUrl: string
    ) => void;
    allowedHostnames?: string[];
    allowRelative?: boolean;
  };

/**
 * SafeExternalLink
 * Reusable, security-hardened anchor that validates destinations before navigation.
 * - Blocks javascript:, data:, vbscript:
 * - Allows relative paths (configurable)
 * - For absolute URLs, only allows http/https and hostnames in allowlist
 */
export const SafeExternalLink: React.FC<SafeExternalLinkProps> = ({
  href,
  onSafeClick,
  allowedHostnames,
  allowRelative = true,
  children,
  ...rest
}) => {
  const defaultAllowed = React.useMemo(
    () =>
      new Set<string>([
        "instagram.com",
        "www.instagram.com",
        "facebook.com",
        "www.facebook.com",
        "x.com",
        "twitter.com",
        "www.twitter.com",
        "youtube.com",
        "www.youtube.com",
        "spotify.com",
        "open.spotify.com",
        "tiktok.com",
        "www.tiktok.com",
        "linkedin.com",
        "www.linkedin.com",
        "behance.net",
        "www.behance.net",
        "dribbble.com",
        "www.dribbble.com",
        "github.com",
        "www.github.com",
        "gitlab.com",
        "www.gitlab.com",
      ]),
    []
  );

  const allowlist = React.useMemo(() => {
    if (allowedHostnames && allowedHostnames.length > 0) {
      return new Set<string>(allowedHostnames);
    }
    return defaultAllowed;
  }, [allowedHostnames, defaultAllowed]);

  const isSafeUrl = React.useCallback(
    (raw: string | undefined | null): raw is string => {
      if (!raw) return false;
      const url = raw.trim();
      if (url.length === 0) return false;

      const lower = url.toLowerCase();
      if (
        lower.startsWith("javascript:") ||
        lower.startsWith("data:") ||
        lower.startsWith("vbscript:")
      ) {
        return false;
      }

      if (allowRelative && url.startsWith("/")) {
        return true;
      }

      try {
        const parsed = new URL(url);
        if (parsed.protocol !== "http:" && parsed.protocol !== "https:")
          return false;
        if (!allowlist.has(parsed.hostname)) return false;
        return true;
      } catch {
        return false;
      }
    },
    [allowRelative, allowlist]
  );

  const urlIsSafe = isSafeUrl(href);
  const safeHref = urlIsSafe ? (href as string) : "#";

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (!urlIsSafe) {
      e.preventDefault();
      if (process.env.NODE_ENV !== "production") {
        console.warn("Blocked unsafe URL navigation:", href);
      }
      return;
    }
    if (onSafeClick) {
      e.preventDefault();
      onSafeClick(e, safeHref);
    }
    // If onSafeClick is not provided, allow default navigation
  };

  return (
    <a
      href={safeHref}
      onClick={handleClick}
      rel={rest.target === "_blank" ? "noopener noreferrer" : rest.rel}
      {...rest}
    >
      {children}
    </a>
  );
};

export default SafeExternalLink;
