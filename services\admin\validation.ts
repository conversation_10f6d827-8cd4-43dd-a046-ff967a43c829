import {
  HEX_COLOR_REGEX,
  PHONE_REGEX,
  EMAIL_REGEX,
  MAX_DESCRIPTION_LENGTH,
  MAX_KEYWORDS_COUNT,
  ERROR_MESSAGES
} from './constants'

// Validation functions
export const validateHexColor = (value: unknown): string | null => {
  const stringValue = String(value || '')
  if (stringValue && !HEX_COLOR_REGEX.test(stringValue)) {
    return ERROR_MESSAGES.INVALID_HEX_COLOR
  }
  return null
}

export const validatePhone = (value: unknown): string | null => {
  const stringValue = String(value || '')
  if (stringValue && !PHONE_REGEX.test(stringValue.replace(/[\s\-\(\)]/g, ''))) {
    return ERROR_MESSAGES.INVALID_PHONE
  }
  return null
}

export const validateEmail = (value: unknown): string | null => {
  const stringValue = String(value || '')
  if (stringValue && !EMAIL_REGEX.test(stringValue)) {
    return ERROR_MESSAGES.INVALID_EMAIL
  }
  return null
}

export const validateUrl = (value: unknown): string | null => {
  const stringValue = String(value || '')
  if (stringValue) {
    try {
      new URL(stringValue)
    } catch {
      return ERROR_MESSAGES.INVALID_URL
    }
  }
  return null
}

export const validateDescription = (value: unknown): string | null => {
  const stringValue = String(value || '')
  if (stringValue && stringValue.length > MAX_DESCRIPTION_LENGTH) {
    return ERROR_MESSAGES.DESCRIPTION_TOO_LONG
  }
  return null
}

export const validateKeywords = (value: unknown): string | null => {
  const stringValue = String(value || '')
  if (stringValue && stringValue.split(',').length > MAX_KEYWORDS_COUNT) {
    return ERROR_MESSAGES.TOO_MANY_KEYWORDS
  }
  return null
}
