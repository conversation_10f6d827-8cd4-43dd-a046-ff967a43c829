"use client";

import React, { forwardRef } from "react";
import { Input } from "@/components/ui/input";
import {
  FormField,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { ValidationErrors } from "./types";
import { Link2, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface UrlInputFieldProps {
  value: string;
  onChange: (value: string) => void;
  validationErrors: ValidationErrors;
  onValidationErrorClear: (field: string) => void;
  disabled?: boolean;
  inputRef?: React.RefObject<HTMLInputElement>;
}

const UrlInputField = forwardRef<HTMLDivElement, UrlInputFieldProps>(({
  value,
  onChange,
  validationErrors,
  onValidationErrorClear,
  disabled = false,
  inputRef,
}, ref) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);

    // Clear validation error when user starts typing
    if (validationErrors.linktreeUrl) {
      onValidationErrorClear("linktreeUrl");
    }
  };

  const hasError = Boolean(validationErrors.linktreeUrl);
  const hasValue = value && value.trim().length > 0;
  const isValid = hasValue && !hasError;

  return (
    <FormField ref={ref}>
      <FormLabel htmlFor="linktree-url" className="flex items-center gap-2">
        <Link2 className="w-4 h-4 text-blue-500" />
        Linktree URL/Username
        <span className="text-red-500">*</span>
      </FormLabel>
      <div className="relative">
        <Input
          ref={inputRef}
          id="linktree-url"
          type="url"
          inputMode="url"
          autoComplete="off"
          autoCorrect="off"
          spellCheck={false}
          data-gramm="false"
          data-ms-editor="false"
          value={value ?? ""}
          onChange={handleChange}
          placeholder="e.g., https://linktr.ee/yourname or yourname"
          className={cn(
            "pl-4 pr-10 transition-all duration-200",
            hasError && "border-red-300 focus:border-red-500 focus:ring-red-500/20",
            isValid && "border-green-300 focus:border-green-500 focus:ring-green-500/20",
            "focus:ring-2 focus:ring-offset-1"
          )}
          aria-invalid={hasError || undefined}
          aria-describedby={
            hasError ? "linktree-url-error" : "linktree-url-description"
          }
          disabled={disabled}
        />
        {isValid && (
          <CheckCircle className="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-green-500" />
        )}
      </div>
      {hasError && (
        <FormMessage id="linktree-url-error" className="animate-slide-down">
          {validationErrors.linktreeUrl}
        </FormMessage>
      )}
      <FormDescription id="linktree-url-description">
        Enter the full Linktree URL or just the username
      </FormDescription>
    </FormField>
  );
});

UrlInputField.displayName = "UrlInputField";

export default UrlInputField;