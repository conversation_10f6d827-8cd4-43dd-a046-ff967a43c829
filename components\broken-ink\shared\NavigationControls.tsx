"use client";

import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

export interface NavigationControlsProps {
  /** Callback for previous button */
  onPrevious: () => void;
  /** Callback for next button */
  onNext: () => void;
  /** Whether previous button should be disabled */
  canScrollPrev?: boolean;
  /** Whether next button should be disabled */
  canScrollNext?: boolean;
  /** Aria label for previous button */
  previousLabel?: string;
  /** Aria label for next button */
  nextLabel?: string;
  /** Total count of items (hides component if <= 1) */
  itemCount?: number;
  /** Additional CSS classes for container */
  className?: string;
  /** Custom button size variant */
  size?: "sm" | "md" | "lg";
}

export const NavigationControls: React.FC<NavigationControlsProps> = ({
  onPrevious,
  onNext,
  canScrollPrev = true,
  canScrollNext = true,
  previousLabel = "Anterior",
  nextLabel = "Próximo",
  itemCount,
  className,
  size = "md",
}) => {
  // Don't render if there's only one item or no items
  if (itemCount !== undefined && itemCount <= 1) {
    return null;
  }

  // Size configurations based on GallerySection style
  const sizeConfig = {
    sm: {
      container: "gap-2",
      button: "w-10 h-10",
      icon: "w-4 h-4",
    },
    md: {
      container: "gap-3",
      button: "w-16 h-16 sm:w-12 sm:h-12",
      icon: "w-4 h-4 sm:w-5 sm:h-5",
    },
    lg: {
      container: "gap-4",
      button: "w-20 h-20 sm:w-16 sm:h-16",
      icon: "w-5 h-5 sm:w-6 sm:h-6",
    },
  };

  const config = sizeConfig[size];
  const defaultContainerClasses = `flex justify-center mt-6 sm:mt-8 ${config.container}`;

  return (
    <div className={className || defaultContainerClasses}>
      <Button
        variant="outline"
        size="sm"
        onClick={onPrevious}
        disabled={!canScrollPrev}
        className={`${config.button} rounded-full p-0 backdrop-blur-sm transition-all duration-200 hover:scale-110 transform bg-white/10 border-white/20 text-white hover:bg-white/20 disabled:opacity-30 disabled:cursor-not-allowed disabled:hover:scale-100`}
        aria-label={previousLabel}
      >
        <ChevronLeft className={config.icon} />
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={onNext}
        disabled={!canScrollNext}
        className={`${config.button} rounded-full p-0 backdrop-blur-sm transition-all duration-200 hover:scale-110 transform bg-white/10 border-white/20 text-white hover:bg-white/20 disabled:opacity-30 disabled:cursor-not-allowed disabled:hover:scale-100`}
        aria-label={nextLabel}
      >
        <ChevronRight className={config.icon} />
      </Button>
    </div>
  );
};
