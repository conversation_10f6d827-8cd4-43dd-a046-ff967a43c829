"use client";

import React from "react";
import { UserProfile } from "@/types/user";
import { transformGalleryData } from "@/lib/brokenInkUtils";
import { SectionHeader } from "@/components/ui/section-header";
import { GalleryCarousel } from "./GalleryCarousel";

interface GallerySectionProps {
  profile: UserProfile;
}

const GallerySection = ({ profile }: GallerySectionProps) => {
  const galleryData = transformGalleryData(profile);

  if (!galleryData.enabled) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24 bg-black" id="gallery">
      <div className="container mx-auto px-4">
        <SectionHeader
          title={galleryData.title}
          description={galleryData.description}
          className="mb-12"
        />

        <GalleryCarousel items={galleryData.galleryItems} />
      </div>
    </section>
  );
};

export default GallerySection;
