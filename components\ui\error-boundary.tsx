"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertTriangle, RefreshCw, Home } from "lucide-react";
import Link from "next/link";

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  showDetails?: boolean;
}

interface ErrorFallbackProps {
  error?: Error;
  errorInfo?: React.ErrorInfo;
  resetError: () => void;
  showDetails?: boolean;
}

class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to monitoring service
    this.props.onError?.(error, errorInfo);

    // In production, you might want to log to a service like Sentry
    if (process.env.NODE_ENV === "production") {
      console.error("Error caught by boundary:", error, errorInfo);
    }
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;

      return (
        <FallbackComponent
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          resetError={this.resetError}
          showDetails={this.props.showDetails}
        />
      );
    }

    // Wrap children in a fragment to handle multiple children
    return <>{this.props.children}</>;
  }
}

// Default error fallback component
function DefaultErrorFallback({
  error,
  errorInfo,
  resetError,
  showDetails = false,
}: ErrorFallbackProps) {
  const [showErrorDetails, setShowErrorDetails] = React.useState(false);

  return (
    <div className="min-h-[400px] flex items-center justify-center p-6">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl">Something went wrong</CardTitle>
          <CardDescription>
            We encountered an unexpected error. Please try refreshing the page
            or contact support if the problem persists.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="flex flex-col gap-2">
            <Button onClick={resetError} className="w-full">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>

            <Button variant="outline" asChild className="w-full">
              <Link href="/">
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Link>
            </Button>
          </div>

          {showDetails && (
            <div className="space-y-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowErrorDetails(!showErrorDetails)}
                className="w-full text-xs"
              >
                {showErrorDetails ? "Hide" : "Show"} Error Details
              </Button>

              {showErrorDetails && (
                <div className="rounded-md bg-muted p-3 text-xs font-mono">
                  <div className="mb-2 font-semibold">Error:</div>
                  <div className="mb-3 text-destructive">
                    {error?.message || "Unknown error"}
                  </div>

                  {error?.stack && (
                    <>
                      <div className="mb-2 font-semibold">Stack Trace:</div>
                      <pre className="whitespace-pre-wrap text-xs opacity-70">
                        {error.stack}
                      </pre>
                    </>
                  )}

                  {errorInfo?.componentStack && (
                    <>
                      <div className="mb-2 mt-3 font-semibold">
                        Component Stack:
                      </div>
                      <pre className="whitespace-pre-wrap text-xs opacity-70">
                        {errorInfo.componentStack}
                      </pre>
                    </>
                  )}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Specialized error boundaries for different contexts
interface PageErrorBoundaryProps extends Omit<ErrorBoundaryProps, "fallback"> {
  children: React.ReactNode;
}

export function PageErrorBoundary({
  children,
  ...props
}: PageErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className="min-h-screen flex items-center justify-center p-6 bg-background">
          <Card className="w-full max-w-lg">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
                <AlertTriangle className="h-8 w-8 text-destructive" />
              </div>
              <CardTitle className="text-2xl">Page Error</CardTitle>
              <CardDescription className="text-base">
                This page encountered an error and couldn't load properly.
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="flex flex-col gap-3">
                <Button onClick={resetError} size="lg" className="w-full">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Reload Page
                </Button>

                <Button variant="outline" size="lg" asChild className="w-full">
                  <Link href="/">
                    <Home className="mr-2 h-4 w-4" />
                    Return Home
                  </Link>
                </Button>
              </div>

              {process.env.NODE_ENV === "development" && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
                    Error Details (Development)
                  </summary>
                  <pre className="mt-2 rounded-md bg-muted p-3 text-xs overflow-auto">
                    {error?.stack || error?.message || "Unknown error"}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      )}
      {...props}
    >
      {children}
    </ErrorBoundary>
  );
}

// Component error boundary for smaller components
export function ComponentErrorBoundary({
  children,
  ...props
}: PageErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="h-4 w-4 text-destructive" />
            <span className="text-sm font-medium text-destructive">
              Component Error
            </span>
          </div>
          <p className="text-xs text-muted-foreground mb-3">
            This component failed to render properly.
          </p>
          <Button size="sm" variant="outline" onClick={resetError}>
            <RefreshCw className="mr-1 h-3 w-3" />
            Retry
          </Button>

          {process.env.NODE_ENV === "development" && (
            <details className="mt-3">
              <summary className="cursor-pointer text-xs text-muted-foreground">
                Debug Info
              </summary>
              <pre className="mt-1 text-xs bg-background rounded p-2 overflow-auto">
                {error?.message || "Unknown error"}
              </pre>
            </details>
          )}
        </div>
      )}
      {...props}
    >
      {children}
    </ErrorBoundary>
  );
}

// Hook for handling async errors in components
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { handleError, resetError };
}

export { ErrorBoundary, DefaultErrorFallback };
export type { ErrorBoundaryProps, ErrorFallbackProps };
