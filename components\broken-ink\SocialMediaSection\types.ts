export interface SocialPlatform {
    iconName: string;
    platformName: string;
    description: string;
    href: string;
}

export interface TransformedSocialMediaData {
    title: string;
    description: string;
    socialPlatforms: SocialPlatform[];
    colors?: {
        background: string;
        linkText: string;
        primary: string;
        secondary: string;
        socialIconBackground: string;
    };
}