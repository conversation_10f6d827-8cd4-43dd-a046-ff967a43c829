import React from "react";
import { UserProfile } from "@/types/user";
import { transformServicesData } from "@/lib/brokenInkUtils";
import { SectionHeader } from "@/components/ui/section-header";
import { ServicesGrid } from "./ServicesGrid";

interface ServicesSectionProps {
  profile: UserProfile;
}

const ServicesSection = ({ profile }: ServicesSectionProps) => {
  const servicesData = transformServicesData(profile);

  if (!servicesData.enabled) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24" id="services">
      <div className="container mx-auto px-4">
        <SectionHeader
          title={servicesData.title}
          description={servicesData.description}
          className="mb-12"
        />

        <ServicesGrid services={servicesData.services} model={profile.model} />
      </div>
    </section>
  );
};

export default ServicesSection;
