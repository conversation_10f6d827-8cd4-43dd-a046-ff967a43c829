import React from "react";
import { cn } from "@/lib/utils";
import { ArtistProfile } from "./ArtistProfile";
import { Artist } from "./types";

interface TeamGridProps {
  artists: Artist[];
}

export const TeamGrid: React.FC<TeamGridProps> = ({ artists }) => {
  return (
    <div
      className={cn(
        "grid gap-6 justify-center transition-all duration-300",
        artists.length === 1 && "grid-cols-1 max-w-md mx-auto",
        artists.length === 2 && "grid-cols-2 max-w-3xl mx-auto",
        artists.length === 3 && "grid-cols-3 max-w-5xl mx-auto"
      )}
    >
      {artists.map((artist, index) => (
        <div
          key={`${artist.name}-${index}`}
          className="w-full"
          style={{ animationDelay: `${index * 0.1}s` }}
        >
          <ArtistProfile
            imageUrl={artist.imageUrl}
            name={artist.name}
            specialty={artist.specialty}
            url={artist.url}
          />
        </div>
      ))}
    </div>
  );
};
