import React from "react";
import { UserProfile } from "@/types/user";
import { SectionHeader } from "@/components/ui/section-header";
import { ReviewCarousel } from "./ReviewCarousel";

interface ReviewsSectionProps {
  profile: UserProfile;
}

const ReviewsSection = ({ profile }: ReviewsSectionProps) => {
  // Check if reviews are enabled and exist
  if (!profile.reviews?.enabled || !profile.reviews?.reviews?.length) {
    return null;
  }

  const reviews = profile.reviews;

  return (
    <section className="py-16 sm:py-24 bg-black overflow-hidden" id="reviews">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <SectionHeader
          title={reviews.title || "Avaliações"}
          description={reviews.description}
          className="mb-12"
        />

        <ReviewCarousel reviews={reviews.reviews} />
      </div>
    </section>
  );
};

export default ReviewsSection;
