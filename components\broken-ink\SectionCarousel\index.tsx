"use client";

import React, { useCallback, useEffect, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { SectionCarouselProps } from "./types";
import { SectionCard } from "./SectionCard";
import { NavigationControls } from "../shared/NavigationControls";
// import { ProgressIndicator } from "../shared/ProgressIndicator";

export const SectionCarousel: React.FC<SectionCarouselProps> = ({
  items,
  sectionType,
  buttonConfig,
  onButtonClick,
}) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: false,
    skipSnaps: false,
    slidesToScroll: "auto",
  });

  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  // const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
    // setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  // const scrollTo = useCallback(
  //   (index: number) => {
  //     if (emblaApi) emblaApi.scrollTo(index);
  //   },
  //   [emblaApi]
  // );

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);

    const timer = setTimeout(() => setIsLoading(false), 100);
    return () => {
      clearTimeout(timer);
      try {
        emblaApi.off("select", onSelect);
        emblaApi.off("reInit", onSelect);
      } catch {
        // no-op if emblaApi instance was disposed
      }
    };
  }, [emblaApi, onSelect]);

  // Labels for navigation based on section type
  const navigationLabels = {
    generic: {
      previous: "Conteúdo anterior",
      next: "Próximo conteúdo",
    },
    features: {
      previous: "Feature anterior",
      next: "Próximo feature",
    },
  };

  return (
    <div className="relative w-full">
      <div
        className={`overflow-hidden rounded-2xl transition-opacity duration-300${
          isLoading ? "opacity-0" : "opacity-100"
        }`}
        ref={emblaRef}
      >
        <div className="flex gap-3 sm:gap-4 lg:gap-6">
          {items.map((item, index) => (
            <div
              key={item.id}
              className="flex-[0_0_80%] sm:flex-[0_0_65%] md:flex-[0_0_50%] lg:flex-[0_0_360px] min-w-0"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <SectionCard
                item={item}
                sectionType={sectionType}
                buttonConfig={buttonConfig}
                onButtonClick={onButtonClick}
              />
            </div>
          ))}
        </div>
      </div>

      <NavigationControls
        onPrevious={scrollPrev}
        onNext={scrollNext}
        canScrollPrev={canScrollPrev}
        canScrollNext={canScrollNext}
        previousLabel={navigationLabels[sectionType].previous}
        nextLabel={navigationLabels[sectionType].next}
      />
      {/* ProgressIndicator Disabled */}
      {/* <ProgressIndicator
        count={items.length}
        selectedIndex={selectedIndex}
        onSelect={scrollTo}
        ariaLabel="Navigate to section"
      /> */}
    </div>
  );
};
