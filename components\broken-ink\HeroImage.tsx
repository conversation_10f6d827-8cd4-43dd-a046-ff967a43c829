import { ScrollContext } from "@/lib/scroll-observer";
import Image from "next/image";
import { useContext, useRef } from "react";

interface HeroProps {
  imageUrl: string;
  alt?: string;
}

const Hero = ({ imageUrl, alt = "hero image" }: HeroProps) => {
  const refContainer = useRef<HTMLDivElement>(null);
  const { scrollY } = useContext(ScrollContext);

  let progress = 0;

  const { current: elContainer } = refContainer;
  if (elContainer) {
    progress = Math.min(1, scrollY / elContainer.clientHeight);
  }
  return (
    <div
      ref={refContainer}
      className="min-h-screen w-full sticky -z-10 top-0 left-0"
      style={{ transform: `translateY(-${progress * 20}vh)` }}
    >
      <Image
        fill
        layout="fill"
        src={imageUrl}
        alt={alt}
        className="object-cover absolute w-full h-full"
      />
    </div>
  );
};

export default Hero;
