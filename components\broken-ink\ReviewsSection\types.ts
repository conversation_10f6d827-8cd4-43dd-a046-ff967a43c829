import { UserProfile, Review } from "@/types/user";

export interface ReviewsData {
    enabled: boolean;
    title?: string;
    description?: string;
    reviews: Review[];
}

export interface ReviewsSectionProps {
    profile: UserProfile;
}

export interface ReviewCardProps {
    review: Review;
    index: number;
}

export interface ReviewCarouselProps {
    reviews: Review[];
}

export interface NavigationControlsProps {
    canScrollPrev: boolean;
    canScrollNext: boolean;
    onScrollPrev: () => void;
    onScrollNext: () => void;
    reviewsCount: number;
}

export interface ProgressIndicatorProps {
    reviewsCount: number;
    selectedIndex: number;
    onScrollTo: (index: number) => void;
}