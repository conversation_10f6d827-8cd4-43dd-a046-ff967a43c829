// Types for Linktree extraction components

export interface LinktreeLink {
  title?: string;
  url: string;
  icon?: string;
}

export interface LinktreeExtraInfo {
  location?: string;
  username?: string;
  description?: string;
  avatar_url?: string;
  user_bio?: string;
}

export interface LinktreeData {
  extra_info?: LinktreeExtraInfo;
  links: LinktreeLink[];
}

export interface LinktreeExtractResult {
  data?: LinktreeData[];
  success?: boolean;
  error?: string;
}

export interface TemplateOption {
  value: string;
  label: string;
  description: string;
}

export interface ValidationErrors {
  [key: string]: string;
}

export interface LinktreeFormData {
  linktreeUrl: string;
  additionalInfo: string;
  selectedTemplate: string;
  autoExtract: boolean;
}
