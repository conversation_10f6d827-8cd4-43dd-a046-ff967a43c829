import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const spinnerVariants = cva("animate-spin rounded-full", {
  variants: {
    variant: {
      default: "border-2 border-muted border-t-primary",
      dots: "border-2 border-dotted border-primary",
      pulse: "bg-primary animate-pulse rounded-full",
      bars: "bg-primary",
    },
    size: {
      xs: "w-3 h-3",
      sm: "w-4 h-4",
      md: "w-6 h-6",
      lg: "w-8 h-8",
      xl: "w-12 h-12",
    },
  },
  defaultVariants: {
    variant: "default",
    size: "md",
  },
});

export interface LoadingSpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  label?: string;
}

const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  ({ className, variant, size, label = "Loading...", ...props }, ref) => {
    if (variant === "bars") {
      return (
        <div
          ref={ref}
          className={cn(
            "flex items-center justify-center space-x-1",
            className
          )}
          role="status"
          aria-label={label}
          {...props}
        >
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                "bg-primary rounded-full animate-pulse",
                size === "xs" && "w-1 h-3",
                size === "sm" && "w-1 h-4",
                size === "md" && "w-1.5 h-6",
                size === "lg" && "w-2 h-8",
                size === "xl" && "w-3 h-12"
              )}
              style={{
                animationDelay: `${i * 0.1}s`,
                animationDuration: "0.6s",
              }}
            />
          ))}
          <span className="sr-only">{label}</span>
        </div>
      );
    }

    return (
      <div
        ref={ref}
        className={cn(spinnerVariants({ variant, size }), className)}
        role="status"
        aria-label={label}
        {...props}
      >
        <span className="sr-only">{label}</span>
      </div>
    );
  }
);

LoadingSpinner.displayName = "LoadingSpinner";

// Convenience component for full-page loading
interface LoadingPageProps {
  message?: string;
  className?: string;
}

const LoadingPage = React.forwardRef<HTMLDivElement, LoadingPageProps>(
  ({ message = "Loading...", className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col items-center justify-center min-h-[400px] space-y-4",
          className
        )}
        {...props}
      >
        <LoadingSpinner size="lg" />
        <p className="text-muted-foreground text-sm">{message}</p>
      </div>
    );
  }
);

LoadingPage.displayName = "LoadingPage";

// Convenience component for inline loading
interface LoadingInlineProps {
  message?: string;
  size?: VariantProps<typeof spinnerVariants>["size"];
  className?: string;
}

const LoadingInline = React.forwardRef<HTMLDivElement, LoadingInlineProps>(
  ({ message, size = "sm", className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("flex items-center space-x-2", className)}
        {...props}
      >
        <LoadingSpinner size={size} />
        {message && (
          <span className="text-sm text-muted-foreground">{message}</span>
        )}
      </div>
    );
  }
);

LoadingInline.displayName = "LoadingInline";

export { LoadingSpinner, LoadingPage, LoadingInline, spinnerVariants };
