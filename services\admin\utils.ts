import { ItemData, SectionData } from './types'

/**
 * Utility function to safely access nested object properties
 */
export function getNestedProperty(obj: Record<string, unknown>, path: string): unknown {
  return path.split('.').reduce((current: unknown, key: string) => {
    return current && typeof current === 'object' ? (current as Record<string, unknown>)[key] : undefined
  }, obj as unknown)
}

/**
 * Utility function to safely set nested object properties
 */
export function setNestedProperty(obj: Record<string, unknown>, path: string, value: unknown): void {
  const keys = path.split('.')
  const lastKey = keys.pop()
  if (!lastKey) return

  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {}
    }
    return current[key] as Record<string, unknown>
  }, obj)

  target[lastKey] = value
}

/**
 * Generate a new ID for items
 */
export function generateNewId(items: ItemData[]): number {
  if (items.length === 0) return 1
  const maxId = Math.max(0, ...items.map(item => Number(item.id) || 0))
  return maxId + 1
}

/**
 * Find item by ID in an array
 */
export function findItemById(items: ItemData[], itemId: string | number): { item: ItemData | undefined; index: number } {
  // Convert itemId to the same type for comparison
  const normalizedId = typeof itemId === 'string' ? Number(itemId) : itemId
  
  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    const currentId = typeof item.id === 'string' ? Number(item.id) : item.id
    
    if (currentId === normalizedId) {
      return { item, index: i }
    }
  }
  
  return { item: undefined, index: -1 }
}

/**
 * Safely parse JSON with error handling
 */
export async function safeJsonParse(response: Response): Promise<unknown | null> {
  try {
    const contentType = response.headers.get('content-type')
    if (contentType && contentType.includes('application/json')) {
      return await response.json()
    } else {
      console.warn(`Expected JSON but received ${contentType}`)
      return null
    }
  } catch (error) {
    console.error('Failed to parse JSON response:', error)
    return null
  }
}

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj))
}
