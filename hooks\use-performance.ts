"use client";

import { useEffect, useRef, useCallback } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  componentName: string;
  timestamp: number;
}

interface UsePerformanceOptions {
  componentName?: string;
  threshold?: number; // Log warning if render time exceeds this (ms)
  enabled?: boolean;
}

// Hook for monitoring component performance
export function usePerformance(options: UsePerformanceOptions = {}) {
  const {
    componentName = 'Unknown Component',
    threshold = 16, // 16ms = 60fps
    enabled = process.env.NODE_ENV === 'development'
  } = options;

  const renderStartTime = useRef<number>(0);
  const renderCount = useRef<number>(0);
  const totalRenderTime = useRef<number>(0);

  const startMeasure = useCallback(() => {
    if (!enabled) return;
    renderStartTime.current = performance.now();
  }, [enabled]);

  const endMeasure = useCallback(() => {
    if (!enabled || renderStartTime.current === 0) return;
    
    const renderTime = performance.now() - renderStartTime.current;
    renderCount.current += 1;
    totalRenderTime.current += renderTime;

    const metrics: PerformanceMetrics = {
      renderTime,
      componentName,
      timestamp: Date.now(),
    };

    // Log performance metrics
    if (renderTime > threshold) {
      console.warn(`🐌 Slow render detected in ${componentName}:`, {
        renderTime: `${renderTime.toFixed(2)}ms`,
        threshold: `${threshold}ms`,
        renderCount: renderCount.current,
        averageRenderTime: `${(totalRenderTime.current / renderCount.current).toFixed(2)}ms`,
      });
    } else if (process.env.NODE_ENV === 'development') {
      console.log(`⚡ ${componentName} rendered in ${renderTime.toFixed(2)}ms`);
    }

    // Reset for next measurement
    renderStartTime.current = 0;

    return metrics;
  }, [enabled, componentName, threshold]);

  const getAverageRenderTime = useCallback(() => {
    if (renderCount.current === 0) return 0;
    return totalRenderTime.current / renderCount.current;
  }, []);

  const resetMetrics = useCallback(() => {
    renderCount.current = 0;
    totalRenderTime.current = 0;
    renderStartTime.current = 0;
  }, []);

  // Start measuring on every render
  useEffect(() => {
    startMeasure();
    return endMeasure;
  });

  return {
    startMeasure,
    endMeasure,
    getAverageRenderTime,
    resetMetrics,
    renderCount: renderCount.current,
    totalRenderTime: totalRenderTime.current,
  };
}

// Hook for monitoring page load performance
export function usePagePerformance() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const measurePagePerformance = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        const metrics = {
          // Core Web Vitals
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          
          // Navigation timing
          dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
          tcpConnection: navigation.connectEnd - navigation.connectStart,
          serverResponse: navigation.responseEnd - navigation.requestStart,
          domProcessing: navigation.domComplete - navigation.responseEnd,
          
          // Total times
          totalLoadTime: navigation.loadEventEnd - navigation.navigationStart,
          timeToFirstByte: navigation.responseStart - navigation.navigationStart,
        };

        console.log('📊 Page Performance Metrics:', metrics);

        // Log warnings for slow metrics
        if (metrics.totalLoadTime > 3000) {
          console.warn('🐌 Slow page load detected:', `${metrics.totalLoadTime}ms`);
        }
        
        if (metrics.timeToFirstByte > 1000) {
          console.warn('🐌 Slow TTFB detected:', `${metrics.timeToFirstByte}ms`);
        }
      }
    };

    // Measure after page load
    if (document.readyState === 'complete') {
      measurePagePerformance();
    } else {
      window.addEventListener('load', measurePagePerformance);
      return () => window.removeEventListener('load', measurePagePerformance);
    }
  }, []);
}

// Hook for monitoring memory usage
export function useMemoryMonitor(interval: number = 10000) {
  useEffect(() => {
    if (typeof window === 'undefined' || !('memory' in performance)) return;

    const monitorMemory = () => {
      const memory = (performance as any).memory;
      
      if (memory) {
        const metrics = {
          usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1048576), // MB
          totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1048576), // MB
          jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
          usage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100), // %
        };

        console.log('🧠 Memory Usage:', metrics);

        // Warn if memory usage is high
        if (metrics.usage > 80) {
          console.warn('⚠️ High memory usage detected:', `${metrics.usage}%`);
        }
      }
    };

    const intervalId = setInterval(monitorMemory, interval);
    return () => clearInterval(intervalId);
  }, [interval]);
}

// Hook for monitoring bundle size and lazy loading
export function useBundleAnalytics() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Monitor resource loading
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        if (entry.entryType === 'resource') {
          const resource = entry as PerformanceResourceTiming;
          
          // Log large resources
          if (resource.transferSize > 500000) { // 500KB
            console.warn('📦 Large resource detected:', {
              name: resource.name,
              size: `${Math.round(resource.transferSize / 1024)}KB`,
              duration: `${resource.duration.toFixed(2)}ms`,
            });
          }
          
          // Log slow resources
          if (resource.duration > 1000) {
            console.warn('🐌 Slow resource loading:', {
              name: resource.name,
              duration: `${resource.duration.toFixed(2)}ms`,
            });
          }
        }
      });
    });

    observer.observe({ entryTypes: ['resource'] });
    
    return () => observer.disconnect();
  }, []);
}

// Utility function to measure function execution time
export function measureExecutionTime<T extends (...args: any[]) => any>(
  fn: T,
  name?: string
): T {
  return ((...args: Parameters<T>) => {
    const start = performance.now();
    const result = fn(...args);
    const end = performance.now();
    
    const executionTime = end - start;
    const functionName = name || fn.name || 'Anonymous Function';
    
    if (executionTime > 10) {
      console.warn(`⏱️ Slow function execution: ${functionName} took ${executionTime.toFixed(2)}ms`);
    } else if (process.env.NODE_ENV === 'development') {
      console.log(`⚡ ${functionName} executed in ${executionTime.toFixed(2)}ms`);
    }
    
    return result;
  }) as T;
}

// Utility function to measure async function execution time
export function measureAsyncExecutionTime<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  name?: string
): T {
  return (async (...args: Parameters<T>) => {
    const start = performance.now();
    const result = await fn(...args);
    const end = performance.now();
    
    const executionTime = end - start;
    const functionName = name || fn.name || 'Anonymous Async Function';
    
    if (executionTime > 100) {
      console.warn(`⏱️ Slow async function: ${functionName} took ${executionTime.toFixed(2)}ms`);
    } else if (process.env.NODE_ENV === 'development') {
      console.log(`⚡ ${functionName} completed in ${executionTime.toFixed(2)}ms`);
    }
    
    return result;
  }) as T;
}
