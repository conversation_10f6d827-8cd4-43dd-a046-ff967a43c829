"use client";

import React from "react";
import { cn } from "@/lib/utils";

export interface ProgressIndicatorProps {
  /** Total number of items/slides */
  count: number;
  /** Currently selected index */
  selectedIndex: number;
  /** Callback when an indicator is clicked */
  onSelect: (index: number) => void;
  /** Additional CSS classes */
  className?: string;
  /** Custom styling for dots */
  dotClassName?: string;
  /** Whether to use button elements instead of divs (better accessibility) */
  useButtons?: boolean;
  /** Aria label for accessibility */
  ariaLabel?: string;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  count,
  selectedIndex,
  onSelect,
  className,
  dotClassName,
  useButtons = false,
  ariaLabel = "Navigate to item",
}) => {
  // Don't render if there's only one item or no items
  if (count <= 1) {
    return null;
  }

  const baseClasses =
    "w-2 h-2 rounded-full transition-all duration-300 cursor-pointer bg-white";
  const defaultContainerClasses = "flex justify-center mt-4 gap-2";

  const handleMouseEnter = (
    e: React.MouseEvent<HTMLElement>,
    index: number
  ) => {
    if (selectedIndex !== index) {
      e.currentTarget.style.opacity = "0.6";
    }
  };

  const handleMouseLeave = (
    e: React.MouseEvent<HTMLElement>,
    index: number
  ) => {
    if (selectedIndex !== index) {
      e.currentTarget.style.opacity = "0.4";
    }
  };

  const getDotStyle = (index: number) => ({
    opacity: selectedIndex === index ? 1 : 0.4,
    transform: selectedIndex === index ? "scale(1.25)" : "scale(1)",
  });

  const renderDot = (index: number) => {
    const dotClasses = cn(baseClasses, dotClassName);
    const commonProps = {
      className: dotClasses,
      style: getDotStyle(index),
      onMouseEnter: (e: React.MouseEvent<HTMLElement>) =>
        handleMouseEnter(e, index),
      onMouseLeave: (e: React.MouseEvent<HTMLElement>) =>
        handleMouseLeave(e, index),
    };

    if (useButtons) {
      return (
        <button
          key={index}
          {...commonProps}
          onClick={() => onSelect(index)}
          aria-label={`${ariaLabel} ${index + 1}`}
          type="button"
        />
      );
    }

    return (
      <div
        key={index}
        {...commonProps}
        onClick={() => onSelect(index)}
        role="button"
        tabIndex={0}
        aria-label={`${ariaLabel} ${index + 1}`}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            onSelect(index);
          }
        }}
      />
    );
  };

  return (
    <div className={cn(defaultContainerClasses, className)}>
      {Array.from({ length: count }).map((_, index) => renderDot(index))}
    </div>
  );
};
