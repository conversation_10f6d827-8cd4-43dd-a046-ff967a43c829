"use client";

import React from "react";
import { UserProfile } from "@/types/user";
import { getButtonConfig } from "@/lib/buttonUtils";
import { SectionHeader } from "@/components/ui/section-header";
import { SectionCarousel } from "../SectionCarousel";

interface FeaturesSectionProps {
  profile: UserProfile;
}

const FeaturesSection = ({ profile }: FeaturesSectionProps) => {
  if (!profile.featuresSection?.enabled) {
    return null;
  }

  const sectionData = profile.featuresSection;
  const buttonConfig = getButtonConfig("features", sectionData.buttonConfig);

  const handleButtonClick = (url: string, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (url && url !== "#" && url.startsWith("https")) {
      window.open(url, "_blank", "noopener,noreferrer");
    }
  };

  return (
    <section className="py-16 sm:py-24 bg-black" id="features">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 flex flex-col items-center">
        <SectionHeader
          title={sectionData.title}
          description={sectionData.description}
          className="mb-12"
        />

        <SectionCarousel
          items={sectionData.items}
          sectionType="features"
          buttonConfig={buttonConfig}
          onButtonClick={handleButtonClick}
        />
      </div>
    </section>
  );
};

export default FeaturesSection;
