"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { adminDataService } from "@/services/adminDataService";
import UserDeleteModal from "./UserDeleteModal";
import {
  Users,
  RefreshCw,
  Search,
  User,
  CheckCircle,
  AlertCircle,
  Trash2,
  ExternalLink,
} from "lucide-react";

export interface UserSelectorProps {
  selectedUser: string | null;
  onUserSelect: (username: string) => void;
  onUserChange?: (username: string) => void;
}

interface UserInfo {
  username: string;
  displayName?: string;
  lastUpdated?: string;
  recordCount?: number;
  isOnline?: boolean;
}

export default function UserSelector({
  selectedUser,
  onUserSelect,
  onUserChange,
}: UserSelectorProps) {
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<UserInfo | null>(null);
  const { toast } = useToast();

  // Load available users
  const loadUsers = async () => {
    try {
      setError(null);
      const usernames = await adminDataService.getAvailableProfiles();

      // Load basic info for each user
      const userInfoPromises = usernames.map(async (username) => {
        try {
          const userData = await adminDataService.loadUserData(username);
          return {
            username,
            displayName: userData?.user?.name || username,
            lastUpdated: new Date().toISOString(), // In real app, get from API
            recordCount: calculateRecordCount(
              (userData as unknown as Record<string, unknown>) || {}
            ),
            isOnline: true, // In real app, check user status
          };
        } catch {
          return {
            username,
            displayName: username,
            lastUpdated: "Unknown",
            recordCount: 0,
            isOnline: false,
          };
        }
      });

      const userInfos = await Promise.all(userInfoPromises);
      setUsers(userInfos);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load users");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle delete button click
  const handleDeleteClick = (user: UserInfo, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent user selection
    setUserToDelete(user);
    setDeleteModalOpen(true);
  };

  // Handle view profile button click
  const handleViewProfileClick = (username: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent user selection
    window.open(`https://link.avenca.site/${username}`, "_blank");
  };

  // Handle successful deletion
  const handleDeleteSuccess = (deletedUser: UserInfo) => {
    // Remove from users list
    setUsers((prev) =>
      prev.filter((user) => user.username !== deletedUser.username)
    );

    // If the deleted user was selected, clear selection
    if (selectedUser === deletedUser.username) {
      onUserSelect("");
      onUserChange?.("");
    }

    // Close modal
    setDeleteModalOpen(false);
    setUserToDelete(null);

    // Show success toast
    toast({
      title: "User Deleted",
      description: `@${deletedUser.username} and all associated data have been permanently deleted.`,
    });
  };

  // Handle modal close
  const handleDeleteModalClose = () => {
    setDeleteModalOpen(false);
    setUserToDelete(null);
  };

  // Calculate total record count for a user
  const calculateRecordCount = (userData: Record<string, unknown>): number => {
    if (!userData) return 0;

    let count = 0;
    const sections = [
      "featuresSection",
      "gallery",
      "servicesSection",
      "reviews",
      "team",
      "links",
      "socialMedia",
    ];

    sections.forEach((section) => {
      const sectionData = userData[section] as
        | Record<string, unknown>
        | unknown[];
      if (
        sectionData &&
        typeof sectionData === "object" &&
        "items" in sectionData
      ) {
        const items = (sectionData as { items: unknown[] }).items;
        if (Array.isArray(items)) {
          count += items.length;
        }
      } else if (Array.isArray(sectionData)) {
        count += sectionData.length;
      }
    });

    return count;
  };

  // Handle user selection
  const handleUserSelect = (username: string) => {
    onUserSelect(username);
    onUserChange?.(username);
  };

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadUsers();
  };

  // Filter users based on search query
  const filteredUsers = users.filter(
    (user) =>
      user.displayName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.username.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Load users on component mount
  useEffect(() => {
    loadUsers();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading users...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-destructive" />
            <div className="text-destructive mb-4">Error loading users</div>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={loadUsers}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            <CardTitle className="text-lg">Select User</CardTitle>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Search Input */}
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {users.length === 0 ? (
          <div className="text-center py-8">
            <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Users Found</h3>
            <p className="text-muted-foreground mb-4">
              No users found in the Firebase database.
            </p>
            <Button onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="text-center py-8">
            <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Users Found</h3>
            <p className="text-muted-foreground mb-4">
              No users match your search criteria. Try adjusting your search.
            </p>
            <Button variant="outline" onClick={() => setSearchQuery("")}>
              Clear Search
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredUsers.map((user) => {
              const isSelected = selectedUser === user.username;

              return (
                <div
                  key={user.username}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-accent ${
                    isSelected ? "border-primary bg-accent" : "border-border"
                  }`}
                  onClick={() => handleUserSelect(user.username)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <User className="h-8 w-8 text-muted-foreground" />
                        {user.isOnline && (
                          <div className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full border-2 border-background"></div>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {user.displayName}
                          </span>
                          {isSelected && (
                            <CheckCircle className="h-4 w-4 text-primary" />
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          @{user.username}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-2">
                        <div className="text-right">
                          <Badge variant="secondary" className="mb-1">
                            {user.recordCount} records
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            {user.isOnline ? "Online" : "Offline"}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) =>
                            handleViewProfileClick(user.username, e)
                          }
                          className="h-8 w-8 p-0 text-primary hover:text-primary hover:bg-primary/10"
                          title={`View profile @${user.username}`}
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => handleDeleteClick(user, e)}
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                          title={`Delete user @${user.username}`}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {selectedUser && (
          <div className="mt-6 p-4 bg-muted rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-4 w-4 text-primary" />
              <span className="font-medium">Selected User</span>
            </div>
            <div className="text-sm text-muted-foreground">
              Managing data for <strong>@{selectedUser}</strong>
            </div>
          </div>
        )}

        {/* Delete Modal */}
        <UserDeleteModal
          isOpen={deleteModalOpen}
          onClose={handleDeleteModalClose}
          user={userToDelete}
          onDeleteSuccess={handleDeleteSuccess}
        />
      </CardContent>
    </Card>
  );
}
