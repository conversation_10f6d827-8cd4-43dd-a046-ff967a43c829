// Accessibility utilities for better WCAG compliance

/**
 * Generate a unique ID for form elements and ARIA attributes
 */
export function generateId(prefix: string = 'id'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Check if a color meets WCAG contrast requirements
 */
export function getContrastRatio(color1: string, color2: string): number {
  const getLuminance = (color: string): number => {
    // Convert hex to RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // Calculate relative luminance
    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
}

/**
 * Check if contrast ratio meets WCAG standards
 */
export function meetsContrastRequirement(
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean {
  const ratio = getContrastRatio(foreground, background);
  
  if (level === 'AAA') {
    return size === 'large' ? ratio >= 4.5 : ratio >= 7;
  }
  
  return size === 'large' ? ratio >= 3 : ratio >= 4.5;
}

/**
 * Announce content to screen readers
 */
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
  if (typeof window === 'undefined') return;

  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;

  document.body.appendChild(announcement);

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

/**
 * Trap focus within a container (for modals, dropdowns, etc.)
 */
export function trapFocus(container: HTMLElement): () => void {
  const focusableElements = container.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  ) as NodeListOf<HTMLElement>;

  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];

  const handleTabKey = (e: KeyboardEvent) => {
    if (e.key !== 'Tab') return;

    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        lastElement.focus();
        e.preventDefault();
      }
    } else {
      if (document.activeElement === lastElement) {
        firstElement.focus();
        e.preventDefault();
      }
    }
  };

  const handleEscapeKey = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      // Find and trigger close button or callback
      const closeButton = container.querySelector('[data-close]') as HTMLElement;
      if (closeButton) {
        closeButton.click();
      }
    }
  };

  container.addEventListener('keydown', handleTabKey);
  container.addEventListener('keydown', handleEscapeKey);

  // Focus first element
  if (firstElement) {
    firstElement.focus();
  }

  // Return cleanup function
  return () => {
    container.removeEventListener('keydown', handleTabKey);
    container.removeEventListener('keydown', handleEscapeKey);
  };
}

/**
 * Manage focus restoration after modal/dialog closes
 */
export class FocusManager {
  private previousActiveElement: HTMLElement | null = null;

  save(): void {
    this.previousActiveElement = document.activeElement as HTMLElement;
  }

  restore(): void {
    if (this.previousActiveElement) {
      this.previousActiveElement.focus();
      this.previousActiveElement = null;
    }
  }
}

/**
 * Check if user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Check if user prefers high contrast
 */
export function prefersHighContrast(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-contrast: high)').matches;
}

/**
 * Get appropriate animation duration based on user preferences
 */
export function getAnimationDuration(defaultDuration: number): number {
  return prefersReducedMotion() ? 0 : defaultDuration;
}

/**
 * ARIA attributes helper
 */
export const aria = {
  expanded: (isExpanded: boolean) => ({ 'aria-expanded': isExpanded }),
  selected: (isSelected: boolean) => ({ 'aria-selected': isSelected }),
  checked: (isChecked: boolean) => ({ 'aria-checked': isChecked }),
  disabled: (isDisabled: boolean) => ({ 'aria-disabled': isDisabled }),
  hidden: (isHidden: boolean) => ({ 'aria-hidden': isHidden }),
  current: (current: string | boolean) => ({ 'aria-current': current }),
  label: (label: string) => ({ 'aria-label': label }),
  labelledBy: (id: string) => ({ 'aria-labelledby': id }),
  describedBy: (id: string) => ({ 'aria-describedby': id }),
  controls: (id: string) => ({ 'aria-controls': id }),
  owns: (id: string) => ({ 'aria-owns': id }),
  live: (politeness: 'polite' | 'assertive' | 'off') => ({ 'aria-live': politeness }),
  atomic: (isAtomic: boolean) => ({ 'aria-atomic': isAtomic }),
  busy: (isBusy: boolean) => ({ 'aria-busy': isBusy }),
  invalid: (isInvalid: boolean) => ({ 'aria-invalid': isInvalid }),
  required: (isRequired: boolean) => ({ 'aria-required': isRequired }),
  readonly: (isReadonly: boolean) => ({ 'aria-readonly': isReadonly }),
  multiline: (isMultiline: boolean) => ({ 'aria-multiline': isMultiline }),
  autocomplete: (value: string) => ({ 'aria-autocomplete': value }),
  haspopup: (hasPopup: boolean | string) => ({ 'aria-haspopup': hasPopup }),
  level: (level: number) => ({ 'aria-level': level }),
  setsize: (size: number) => ({ 'aria-setsize': size }),
  posinset: (position: number) => ({ 'aria-posinset': position }),
};

/**
 * Keyboard navigation helpers
 */
export const keyboard = {
  isEnterOrSpace: (event: KeyboardEvent) => 
    event.key === 'Enter' || event.key === ' ',
  
  isArrowKey: (event: KeyboardEvent) =>
    ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key),
  
  isEscape: (event: KeyboardEvent) => event.key === 'Escape',
  
  isTab: (event: KeyboardEvent) => event.key === 'Tab',
  
  isHome: (event: KeyboardEvent) => event.key === 'Home',
  
  isEnd: (event: KeyboardEvent) => event.key === 'End',
  
  preventDefault: (event: KeyboardEvent) => {
    event.preventDefault();
    event.stopPropagation();
  },
};

/**
 * Screen reader utilities
 */
export const screenReader = {
  only: 'sr-only',
  
  announce: announceToScreenReader,
  
  hide: (element: HTMLElement) => {
    element.setAttribute('aria-hidden', 'true');
  },
  
  show: (element: HTMLElement) => {
    element.removeAttribute('aria-hidden');
  },
  
  setLabel: (element: HTMLElement, label: string) => {
    element.setAttribute('aria-label', label);
  },
  
  setDescription: (element: HTMLElement, description: string, descriptionId?: string) => {
    const id = descriptionId || generateId('desc');
    element.setAttribute('aria-describedby', id);
    
    // Create description element if it doesn't exist
    if (!document.getElementById(id)) {
      const descElement = document.createElement('div');
      descElement.id = id;
      descElement.className = 'sr-only';
      descElement.textContent = description;
      document.body.appendChild(descElement);
    }
  },
};

/**
 * Color accessibility helpers
 */
export const colorA11y = {
  getContrastRatio,
  meetsContrastRequirement,
  
  suggestBetterColor: (foreground: string, background: string, target: 'AA' | 'AAA' = 'AA'): string => {
    // This is a simplified implementation
    // In a real app, you'd want a more sophisticated color adjustment algorithm
    const ratio = getContrastRatio(foreground, background);
    const requiredRatio = target === 'AAA' ? 7 : 4.5;
    
    if (ratio >= requiredRatio) return foreground;
    
    // Simple approach: darken or lighten the foreground color
    const hex = foreground.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    // Determine if we should darken or lighten
    const shouldDarken = getContrastRatio('#000000', background) > getContrastRatio('#ffffff', background);
    const factor = shouldDarken ? 0.8 : 1.2;
    
    const newR = Math.min(255, Math.max(0, Math.round(r * factor)));
    const newG = Math.min(255, Math.max(0, Math.round(g * factor)));
    const newB = Math.min(255, Math.max(0, Math.round(b * factor)));
    
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  },
};
