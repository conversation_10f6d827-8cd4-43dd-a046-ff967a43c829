import React from "react";
import useEmblaCarousel from "embla-carousel-react";
import { ArtistProfile } from "./ArtistProfile";
import { ProgressIndicator } from "@/components/broken-ink/shared/ProgressIndicator";
import { useCarousel } from "@/components/hooks/useCarousel";
import { Artist } from "./types";

interface TeamCarouselProps {
  artists: Artist[];
}

export const TeamCarousel: React.FC<TeamCarouselProps> = ({ artists }) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: true,
  });

  const { selectedIndex, scrollTo } = useCarousel(emblaApi);

  return (
    <div className="relative w-full">
      <div className="overflow-hidden rounded-xl" ref={emblaRef}>
        <div className="flex gap-3 sm:gap-4 lg:gap-6 ml-0">
          {artists.map((artist, index) => (
            <div
              key={`${artist.name}-${index}`}
              className="flex-[0_0_calc(100%-2rem)] sm:flex-[0_0_calc(70%-1rem)] md:flex-[0_0_calc(50%-1rem)] lg:flex-[0_0_360px] min-w-0 max-w-full mx-auto"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <ArtistProfile
                imageUrl={artist.imageUrl}
                name={artist.name}
                specialty={artist.specialty}
                url={artist.url}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Progress indicator */}
      <div className="mt-8">
        <ProgressIndicator
          count={artists.length}
          selectedIndex={selectedIndex}
          onSelect={scrollTo}
          ariaLabel="Navigate to artist"
        />
      </div>
    </div>
  );
};
