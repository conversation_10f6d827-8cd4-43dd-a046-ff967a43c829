import React from "react";

export const LoadingCard: React.FC = () => {
  return (
    <div role="status" aria-busy="true" aria-live="polite" className="w-full">
      <span className="sr-only">Loading content…</span>
      <div className="animate-pulse">
        <div
          className="rounded-3xl bg-gray-800/50 backdrop-blur-3xl w-full p-6 ring-1 ring-black/10"
          aria-hidden="true"
        >
          <div className="flex items-center gap-4" aria-hidden="true">
            <div className="bg-gray-700/50 p-4 rounded-3xl w-14 h-14 flex-shrink-0"></div>
            <div className="space-y-2 flex-1">
              <div className="h-5 bg-gray-700/50 rounded w-3/4"></div>
              <div className="h-4 bg-gray-700/50 rounded w-full"></div>
            </div>
          </div>
          <div className="mt-4 space-y-3" aria-hidden="true">
            <div className="h-28 w-full rounded-xl bg-gray-700/40" />
            <div className="h-10 w-1/3 rounded-lg bg-gray-700/40" />
          </div>
        </div>
      </div>
    </div>
  );
};
