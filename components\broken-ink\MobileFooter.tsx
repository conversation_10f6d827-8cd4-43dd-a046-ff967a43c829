import React from "react";
import { UserProfile } from "@/types/user";

const HomeIcon = () => (
  <svg
    fill="currentColor"
    height="24px"
    viewBox="0 0 256 256"
    width="24px"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M224,115.55V208a16,16,0,0,1-16,16H168a16,16,0,0,1-16-16V168a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v40a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V115.55a16,16,0,0,1,5.17-11.78l80-75.48.11-.11a16,16,0,0,1,21.53,0,1.14,1.14,0,0,0,.11.11l80,75.48A16,16,0,0,1,224,115.55Z"></path>
  </svg>
);

const GalleryIcon = () => (
  <svg
    fill="currentColor"
    height="24px"
    viewBox="0 0 256 256"
    width="24px"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,16V158.75l-26.07-26.06a16,16,0,0,0-22.63,0l-20,20-44-44a16,16,0,0,0-22.62,0L40,149.37V56ZM40,172l52-52,80,80H40Zm176,28H194.63l-36-36,20-20L216,181.38V200ZM144,100a12,12,0,1,1,12,12A12,12,0,0,1,144,100Z"></path>
  </svg>
);

const ArtistsIcon = () => (
  <svg
    fill="currentColor"
    height="24px"
    viewBox="0 0 256 256"
    width="24px"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M117.25,157.92a60,60,0,1,0-66.5,0A95.83,95.83,0,0,0,3.53,195.63a8,8,0,1,0,13.4,8.74,80,80,0,0,1,134.14,0,8,8,0,0,0,13.4-8.74A95.83,95.83,0,0,0,117.25,157.92ZM40,108a44,44,0,1,1,44,44A44.05,44.05,0,0,1,40,108Zm210.14,98.7a8,8,0,0,1-11.07-2.33A79.83,79.83,0,0,0,172,168a8,8,0,0,1,0-16,44,44,0,1,0-16.34-84.87,8,8,0,1,1-5.94-14.85,60,60,0,0,1,55.53,105.64,95.83,95.83,0,0,1,47.22,37.71A8,8,0,0,1,250.14,206.7Z"></path>
  </svg>
);

const BookIcon = () => (
  <svg
    fill="currentColor"
    height="24px"
    viewBox="0 0 256 256"
    width="24px"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM72,48v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24V80H48V48ZM208,208H48V96H208V208Zm-96-88v64a8,8,0,0,1-16,0V132.94l-4.42,2.22a8,8,0,0,1-7.16-14.32l16-8A8,8,0,0,1,112,120Zm59.16,30.45L152,176h16a8,8,0,0,1,0,16H136a8,8,0,0,1-6.4-12.8l28.78-38.37A8,8,0,1,0,145.07,132a8,8,0,1,1-13.85-8A24,24,0,0,1,176,136,23.76,23.76,0,0,1,171.16,150.45Z"></path>
  </svg>
);

const footerLinks = [
  { href: "#home", icon: <HomeIcon />, label: "Home", current: true },
  { href: "#gallery", icon: <GalleryIcon />, label: "Galeria" },
  { href: "#artists", icon: <ArtistsIcon />, label: "Artists" },
  { href: "#contact", icon: <BookIcon />, label: "Book" }, // Assuming "Book" links to contact/booking
];

interface MobileFooterProps {
  profile: UserProfile;
}

const MobileFooter = ({ profile }: MobileFooterProps) => {
  return (
    <footer className="sticky bottom-0 bg-gray-900/80 backdrop-blur-sm border-t border-gray-800 md:hidden">
      <div className="container mx-auto">
        <div className="flex justify-around items-center pt-2 pb-3">
          {footerLinks.map((link) => (
            <a
              key={link.label}
              className={`flex flex-col items-center justify-end gap-1 ${
                link.current ? "text-white" : "text-gray-400 hover:text-white"
              } transition-colors`}
              href={link.href}
            >
              <div className="flex h-8 items-center justify-center">
                {link.icon}
              </div>
              <p className="text-xs font-medium leading-normal tracking-wide">
                {link.label}
              </p>
            </a>
          ))}
        </div>
      </div>
    </footer>
  );
};

export default MobileFooter;
