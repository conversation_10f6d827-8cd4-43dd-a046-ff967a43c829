"use client";

import React, { useEffect, useState } from "react";
import { UserProfile } from "@/types/user";
import { transformLinksData } from "@/lib/brokenInkUtils";
import { useScreenSize } from "../../hooks/useScreenSize";
import { useLinkNavigation } from "@/components/hooks/useLinkNavigation";
import { SectionHeader } from "@/components/ui/section-header";
import { LinkGrid } from "./LinkGrid";
import { LinkCarousel } from "./LinkCarousel";

interface LinksSectionProps {
  profile: UserProfile;
}

const LinksSection = ({ profile }: LinksSectionProps) => {
  const linksData = transformLinksData(profile);
  const { isMounted, isLargeScreen } = useScreenSize();
  const { handleLinkClick } = useLinkNavigation();
  const [isLoading, setIsLoading] = useState(true);

  // Check if we should use grid layout (1-3 cards on large screens)
  const shouldUseGrid =
    isMounted && isLargeScreen && linksData.links.length <= 3;

  useEffect(() => {
    // Handle loading state
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  // Don't render if no links and not loading
  if (!isLoading && linksData.links.length === 0) {
    return null;
  }

  return (
    <section className="pb-16 sm:pb-24 bg-black max-w-7xl mx-auto" id="links">
      <div className="px-4 sm:px-6 lg:px-8 flex flex-col items-center">
        <div className="container mx-auto">
          <SectionHeader
            title={""}
            description={linksData.description}
            className="mb-12"
          />

          {/* Adaptive Layout: Grid for 1-3 cards on large screens, Carousel otherwise */}
          {shouldUseGrid ? (
            <LinkGrid
              links={linksData.links}
              onLinkClick={handleLinkClick}
              isLoading={isLoading}
            />
          ) : (
            <LinkCarousel
              links={linksData.links}
              onLinkClick={handleLinkClick}
              isLoading={isLoading}
            />
          )}
        </div>
      </div>
    </section>
  );
};

export default LinksSection;
