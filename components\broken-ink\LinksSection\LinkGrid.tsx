import React from "react";
import { cn } from "@/lib/utils";
import { getIconComponent } from "@/lib/iconUtils";
import { LinkCard } from "./LinkCard";
import { TransformedLink } from "./types";

interface LinkGridProps {
  links: TransformedLink[];
  onLinkClick: (url: string, event: React.MouseEvent) => void;
  isLoading: boolean;
}

export const LinkGrid: React.FC<LinkGridProps> = ({
  links,
  onLinkClick,
  isLoading,
}) => {
  return (
    <div
      className={cn(
        "grid gap-4 justify-center transition-all duration-300",
        links.length === 1 && "grid-cols-1 max-w-md mx-auto",
        links.length === 2 && "grid-cols-2 max-w-3xl mx-auto",
        links.length === 3 && "grid-cols-3 max-w-5xl mx-auto"
      )}
    >
      {links.map((link, index) => {
        const IconComponent = getIconComponent(link.iconName);
        return (
          <div key={index} className="w-full p-2">
            <LinkCard
              icon={
                !isLoading ? (
                  <IconComponent
                    className="h-6 w-6 text-white"
                    aria-hidden="true"
                    focusable="false"
                  />
                ) : null
              }
              title={link.text}
              description={link.description}
              onClick={(e) => onLinkClick(link.url, e)}
              isLoading={isLoading}
            />
          </div>
        );
      })}
    </div>
  );
};
