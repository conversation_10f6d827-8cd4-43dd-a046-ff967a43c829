import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function GlobalNotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-muted/20 to-background p-4">
      <div className="w-full max-w-2xl">
        {/* Animated 404 Section */}
        <div className="text-center mb-12 animate-fade-in">
          <div className="relative mb-8">
            <h1 className="text-8xl md:text-9xl font-black text-transparent bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text animate-pulse">
              404
            </h1>
            <div className="absolute inset-0 text-8xl md:text-9xl font-black text-primary/10 blur-sm">
              404
            </div>
          </div>

          <div className="space-y-4 animate-slide-up">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground">
              Ops.. Página Não Encontrada
            </h2>
            <p className="text-lg text-muted-foreground max-w-lg mx-auto leading-relaxed">
              A página que você está procurando não existe ou foi movida.
            </p>
          </div>
        </div>

        {/* Action Button */}
        <div className="text-center animate-slide-up">
          <Button
            asChild
            size="lg"
            animation="hover"
            className="min-w-48 rounded-3xl"
          >
            <Link href="/">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar ao Início
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
