# GenericSection - Componente Modular

O `GenericSection` é um componente React modular e reutilizável que pode renderizar seções de conteúdo em diferentes layouts (grid ou carousel) com configurações personalizáveis.

## Características

- ✅ **Modular**: Um único componente para múltiplas seções
- ✅ **Flexível**: Suporte a layout grid e carousel
- ✅ **Responsivo**: Design adaptável para todos os dispositivos
- ✅ **Customizável**: Textos e comportamentos personalizáveis
- ✅ **Acessível**: Aria-labels e navegação por teclado

## Interface

```typescript
interface GenericSectionProps {
  sectionData: SectionData;           // Dados da seção (obrigatório)
  layout?: 'grid' | 'carousel';       // Layout da seção (padrão: 'grid')
  primaryButtonText?: string;         // Texto do botão primário (padrão: 'Ver Mais')
  secondaryButtonText?: string;       // Texto do botão secundário (padrão: 'Contato')
  showBadge?: boolean;               // Mostrar badge numerado (padrão: false)
  sectionType?: 'features' | 'services' | 'generic'; // Tipo para aria-labels (padrão: 'generic')
}
```

## Exemplos de Uso

### 1. Features Section (Carousel)
```jsx
<GenericSection 
  sectionData={data.featuresSection}
  layout="carousel"
  primaryButtonText="Ver"
  secondaryButtonText="Contato"
  showBadge={false}
  sectionType="features"
/>
```

### 2. Services Section (Carousel com Badges)
```jsx
<GenericSection 
  sectionData={data.servicesSection}
  layout="carousel"
  primaryButtonText="Solicitar"
  secondaryButtonText="Info"
  showBadge={true}
  sectionType="services"
/>
```

### 3. Generic Section (Grid)
```jsx
<GenericSection 
  sectionData={data.genericSection}
  layout="grid"
  primaryButtonText="Ver Mais"
  secondaryButtonText="Contato"
  showBadge={false}
  sectionType="generic"
/>
```

## Estrutura de Dados Esperada

```typescript
interface SectionData {
  title: string;                    // Título da seção
  description: string;              // Descrição da seção
  enabled: boolean;                 // Se a seção está habilitada
  items: SectionItem[];             // Array de itens
}

interface SectionItem {
  id: number;                       // ID único do item
  title: string;                    // Título do item
  description: string;              // Descrição do item
  image: string;                    // URL da imagem
  primaryButton: {                  // Configuração do botão primário
    icon: string;                   // Classe do ícone Font Awesome
    url: string;                    // URL de destino
  };
  secondaryButton: {                // Configuração do botão secundário
    icon: string;                   // Classe do ícone Font Awesome
    url: string;                    // URL de destino
  };
}
```

## Layouts Disponíveis

### Grid Layout
- **Uso**: Ideal para seções com poucos itens que devem ser todos visíveis
- **Comportamento**: Grid responsivo (1/2/3 colunas)
- **Animação**: Fade-in sequencial dos cards

### Carousel Layout
- **Uso**: Ideal para seções com muitos itens ou quando o espaço é limitado
- **Comportamento**: Carrossel horizontal com navegação
- **Recursos**: Botões prev/next, indicadores de progresso, drag/swipe

## Funcionalidades Especiais

### Badge Numerado
- Quando `showBadge={true}`, mostra um badge circular no canto superior direito
- Útil para enumerar serviços ou recursos
- Animação de scale no hover
- Tamanho maior (40px) e melhor contraste

### Lógica Inteligente de Botões (Inspirada no Flutter)
- **Renderização Condicional**: Botões só aparecem se URL não for '#'
- **Layout Adaptativo**: 
  - Se ambos os botões existem: primário expandido + secundário apenas ícone
  - Se apenas um existe: ocupa toda a largura disponível
- **Estilo Premium**: Backdrop blur e transparências
- **Posicionamento**: Botões posicionados sobre a imagem (como no Flutter)

### Cards Aprimorados
- **Altura Fixa**: 400px para consistência visual
- **Overlay Gradiente**: Gradiente mais intenso para melhor legibilidade
- **Conteúdo na Imagem**: Título, descrição e botões sobre a imagem
- **Efeitos Visuais**: Sombras e backdrop blur para profundidade

### Indicadores de Progresso Inteligentes
- **Estado Ativo**: Indicador atual com opacity 100% e scale 125%
- **Estados Inativos**: Opacity 40% com hover para 60%
- **Transições Suaves**: Animações de 300ms
- **Clicáveis**: Navegação direta ao clicar nos dots

### Responsividade
- **Mobile**: Cards ocupam 90% da largura
- **Tablet**: Cards ocupam 85% da largura
- **Desktop**: Cards têm largura fixa de 360px
- **Altura Consistente**: 400px em todas as telas para uniformidade

## Vantagens da Modularização

1. **Manutenibilidade**: Um único componente para manter
2. **Consistência**: Visual e comportamento uniformes
3. **Reutilização**: Pode ser usado em outras partes da aplicação
4. **Flexibilidade**: Configurável via props
5. **Performance**: Código otimizado e sem duplicação

## Migração dos Componentes Antigos

### Antes (3 componentes separados)
- `FeaturesSection.tsx` (140 linhas)
- `ServicesSection.tsx` (183 linhas)
- `GenericSection.tsx` (antigo - 85 linhas)

### Depois (1 componente modular)
- `GenericSection.tsx` (226 linhas)
- **Redução**: ~182 linhas de código
- **Funcionalidades**: Todas mantidas + melhorias
