import React from "react";
import { UserProfile } from "@/types/user";
import {
  transformContactData,
  transformLocationData,
} from "@/lib/brokenInkUtils";
import { ContactInfo } from "./ContactInfo";
import { LocationInfo } from "./LocationInfo";
import { SocialLinks } from "./SocialLinks";

interface ContactSectionProps {
  profile: UserProfile;
}

const ContactSection = ({ profile }: ContactSectionProps) => {
  const contactData = transformContactData(profile);
  const locationData = transformLocationData(profile);

  return (
    <section className="py-16 sm:py-24 bg-black" id="contact">
      <div className="container mx-auto px-4">
        <LocationInfo locationData={locationData} />

        <ContactInfo contactData={contactData} userName={profile.user.name} />

        <SocialLinks contactData={contactData} />
      </div>
    </section>
  );
};

export default ContactSection;
