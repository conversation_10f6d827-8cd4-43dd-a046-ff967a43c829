export interface GalleryItem {
    imageUrl: string;
    title: string;
    altText: string;
}

export interface GalleryData {
    enabled: boolean;
    title: string;
    description?: string;
    galleryItems: GalleryItem[];
}

export interface GalleryCarouselProps {
    items: GalleryItem[];
}

export interface GalleryImageProps {
    item: GalleryItem;
    index: number;
    totalItems: number;
    imageDimensions: { [key: string]: { width: number; height: number } };
}
