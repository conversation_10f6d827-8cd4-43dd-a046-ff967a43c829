import React from "react";
import { useLinkNavigation } from "@/components/hooks/useLinkNavigation";

interface SocialCardProps {
  icon: React.ReactNode;
  platformName: string;
  description: string;
  href?: string;
}

export const SocialCard: React.FC<SocialCardProps> = ({
  icon,
  platformName,
  description,
  href = "#",
}) => {
  const { handleLinkClick } = useLinkNavigation();

  return (
    <a
      className="group relative flex flex-col justify-between rounded-3xl bg-custom backdrop-blur-3xl md:w-[340px] w-full max-w-sm sm:max-w-xs p-6 ring-1 ring-black/10 transition-all hover:ring-white/20"
      href={href}
      onClick={(e) => {
        // Prevent default to delegate navigation to our handler (SPA routing / safe external)
        e.preventDefault();
        handleLinkClick(href, e);
      }}
      onKeyDown={(e) => {
        // Support keyboard activation (Enter/Space) for accessibility
        // Ignore if any modifier keys are pressed
        if (e.alt<PERSON>ey || e.ctrlKey || e.metaKey || e.shiftKey) return;

        const isEnter = e.key === "Enter";
        const isSpace =
          e.key === " " || e.key === "Spacebar" || e.code === "Space";

        if (isEnter || isSpace) {
          // Prevent default to avoid native scroll (Space) and duplicate native navigation
          e.preventDefault();
          handleLinkClick(
            href,
            e as unknown as React.MouseEvent<HTMLAnchorElement, MouseEvent>
          );
        }
      }}
    >
      <div className="flex items-center gap-4 sm:flex-col sm:items-center sm:gap-3">
        <div className="bg-black/40 p-6 rounded-3xl">{icon}</div>
        <div className="sm:text-center">
          <p className="text-lg font-semibold text-white">{platformName}</p>
          <p className="mt-2 text-gray-400 sm:mt-2">{description}</p>
        </div>
      </div>
    </a>
  );
};
