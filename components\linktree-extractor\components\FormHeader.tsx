"use client";

import React from "react";
import { Link2, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface FormHeaderProps {
  className?: string;
}

export default function FormHeader({ className }: FormHeaderProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-3">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg blur-sm opacity-75"></div>
          <div className="relative bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-lg">
            <Link2 className="w-6 h-6 text-white" />
          </div>
        </div>
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Linktree Extractor
            </h1>
            <Badge variant="secondary" className="text-xs font-medium">
              <Sparkles className="w-3 h-3 mr-1" />
              AI-Powered
            </Badge>
          </div>
          <p className="text-muted-foreground text-sm mt-1">
            Cole a URL do Linktree, escolha um template e, se quiser, adicione instruções.
          </p>
        </div>
      </div>
    </div>
  );
}