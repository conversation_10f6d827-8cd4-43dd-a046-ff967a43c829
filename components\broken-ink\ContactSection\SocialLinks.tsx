import React from "react";
import { getIconComponent } from "@/lib/iconUtils";
import { ContactData } from "./types";
import { SafeExternalLink } from "@/components/security/SafeExternalLink";

interface SocialLinksProps {
  contactData: ContactData;
}

export const SocialLinks = ({ contactData }: SocialLinksProps) => {
  if (contactData.socialMedia.length === 0) {
    return null;
  }

  return (
    <div className="mt-12">
      <div className="flex justify-center gap-6">
        {contactData.socialMedia.map((social) => {
          const iconName = social.classIcon?.includes("fa-")
            ? social.classIcon.split("fa-")[1]
            : "globe";
          const IconComponent = getIconComponent(iconName);
          return (
            <SafeExternalLink
              key={social.text}
              className="text-gray-400 hover:text-white transition-colors"
              href={social.url}
              aria-label={social.text}
            >
              <IconComponent className="h-7 w-7" aria-hidden="true" />
            </SafeExternalLink>
          );
        })}
      </div>
    </div>
  );
};
