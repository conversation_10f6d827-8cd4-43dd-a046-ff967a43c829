import {
  UserProfile,
  ApiResponse,
  UserNotFoundError,
  InvalidUsernameError,
  validateUserProfile
} from '@/types'
import { dataService } from './dataService'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api'
const FIREBASE_API_BASE_URL = process.env.NEXT_PUBLIC_FIREBASE_API_BASE_URL || 'https://cardlink-bio-default-rtdb.firebaseio.com/users'

class ApiService {
  private async fetchApi<T>(endpoint: string, options?: RequestInit): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      })

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`

        // Try to parse error response as JSON, but handle HTML responses gracefully
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.message || errorMessage
          } else {
            // If it's not JSON (likely HTML error page), use status text
            errorMessage = response.statusText || errorMessage
          }
        } catch (parseError) {
          // If JSON parsing fails, stick with the HTTP status message
          console.warn('Failed to parse error response:', parseError)
        }

        throw new Error(errorMessage)
      }

      return await response.json()
    } catch (error) {
      console.error(`API Error for ${endpoint}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }

  private async fetchFirebaseApi<T>(username: string, options?: RequestInit): Promise<T | null> {
    try {
      const url = `${FIREBASE_API_BASE_URL}/${username}.json`
      console.log(`Fetching Firebase data from: ${url}`)

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      })

      console.log(`Firebase API response status: ${response.status}`)

      if (!response.ok) {
        if (response.status === 404) {
          console.log(`User ${username} not found in Firebase`)
          return null
        }
        throw new Error(`Firebase API error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log(`Firebase data received for ${username}:`, data ? 'Data found' : 'No data')

      // Firebase returns null for non-existent paths
      if (data === null) {
        return null
      }

      return data
    } catch (error) {
      console.error(`Firebase API Error for ${username}:`, error)
      throw error
    }
  }

  private isValidUsername(username: string): boolean {
    // More flexible username validation for Firebase usernames
    if (!username || username.length < 2) return false
    // Allow letters, numbers, hyphens, underscores, and dots
    const usernameRegex = /^[a-zA-Z0-9._-]+$/
    return usernameRegex.test(username)
  }

  async getUserProfile(username: string): Promise<UserProfile> {
    if (!this.isValidUsername(username)) {
      throw new InvalidUsernameError(username)
    }

    try {
      console.log(`Attempting to fetch user profile for: ${username}`)

      // Try Firebase API first
      const firebaseData = await this.fetchFirebaseApi<UserProfile>(username)

      if (firebaseData) {
        console.log(`Successfully loaded Firebase data for ${username}`)

        // Validate the data structure (but don't fail if invalid)
        const validation = validateUserProfile(firebaseData)
        if (!validation.isValid) {
          console.warn(`Invalid user profile data for ${username}:`, validation.errors)
          // Continue anyway - the data might still be usable
        }

        return firebaseData
      }

      // If Firebase data not found, try local data service as fallback
      console.log(`No data found in Firebase for ${username}, trying local data service`)
      const localData = await dataService.loadUserData(username)

      if (localData) {
        console.log(`Successfully loaded local data for ${username}`)
        return localData
      }

      console.log(`No data found in local files for ${username}`)
      throw new UserNotFoundError(username)

    } catch (error) {
      if (error instanceof UserNotFoundError || error instanceof InvalidUsernameError) {
        throw error
      }

      // Log the error and re-throw as UserNotFoundError
      console.error(`Error fetching user profile for ${username}:`, error)
      throw new UserNotFoundError(username)
    }
  }

  async incrementLinkClick(linkId: string): Promise<void> {
    await this.fetchApi(`/links/${linkId}/click`, {
      method: 'POST',
    })
  }

  async incrementProfileView(username: string): Promise<void> {
    await this.fetchApi(`/users/${username}/view`, {
      method: 'POST',
    })
  }

  async searchUsers(query: string): Promise<UserProfile[]> {
    try {
      // For now, return empty array as search is not implemented in Firebase
      // This could be enhanced to search through known usernames
      console.log(`Search not implemented for query: ${query}`)
      return []
    } catch (error) {
      console.error('Error searching users:', error)
      return []
    }
  }

  async getUserStats(): Promise<{ totalUsers: number; totalLinks: number; totalClicks: number }> {
    try {
      // For now, return basic stats as Firebase doesn't provide aggregated stats
      console.log('User stats not implemented for Firebase')
      return { totalUsers: 0, totalLinks: 0, totalClicks: 0 }
    } catch (error) {
      console.error('Error getting user stats:', error)
      return { totalUsers: 0, totalLinks: 0, totalClicks: 0 }
    }
  }
}

export const apiService = new ApiService()
export default apiService
