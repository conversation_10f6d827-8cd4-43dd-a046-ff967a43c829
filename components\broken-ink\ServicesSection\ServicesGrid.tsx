import React from "react";
import { getIconComponent } from "@/lib/iconUtils";
import { ServiceCard } from "./ServiceCard";
import { Service } from "./types";
import { Menu, Scissors, Brush, Dumbbell } from "lucide-react";

interface ServicesGridProps {
  services: Service[];
  model?: string;
}

export const ServicesGrid: React.FC<ServicesGridProps> = ({
  services,
  model,
}) => {
  const getModelIcon = (m?: string): React.ReactNode => {
    const sizeClass = "h-8 w-8";
    switch ((m || "").toLowerCase()) {
      case "gym":
        return <Dumbbell className={sizeClass} />;
      case "tattoo":
        return <Brush className={sizeClass} />;
      case "barber":
        return <Scissors className={sizeClass} />;
      case "general":
      default:
        return <Menu className={sizeClass} />;
    }
  };

  return (
    <div className="flex flex-wrap items-center justify-center gap-6 mx-auto">
      {services.map((service) => {
        const icon = service.iconName
          ? (() => {
              const IconComponent = getIconComponent(service.iconName);
              return (
                <IconComponent
                  className="h-8 w-8"
                  role="img"
                  aria-label={service.title + " icon"}
                />
              );
            })()
          : getModelIcon(model);

        return (
          <ServiceCard
            key={service.title}
            icon={icon}
            title={service.title}
            description={service.description}
          />
        );
      })}
    </div>
  );
};
