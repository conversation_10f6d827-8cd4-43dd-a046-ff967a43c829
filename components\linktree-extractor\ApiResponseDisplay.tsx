"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LinktreeExtractResult } from "./types";
import { templateOptions } from "./constants";

interface ApiResponseDisplayProps {
  apiResponse: LinktreeExtractResult | null;
  isLoading: boolean;
  selectedTemplate: string;
}

export default function ApiResponseDisplay({
  apiResponse,
  isLoading,
  selectedTemplate,
}: ApiResponseDisplayProps) {
  const handleCopyJson = () => {
    if (apiResponse) {
      navigator.clipboard.writeText(JSON.stringify(apiResponse, null, 2));
    }
  };

  const getSelectedTemplateName = () => {
    return templateOptions.find((t) => t.value === selectedTemplate)?.label;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          API Response
          {selectedTemplate !== "general" && (
            <span className="text-sm font-normal text-muted-foreground">
              ({getSelectedTemplateName()} Template)
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading && (
          <div className="flex flex-col justify-center items-center py-8 space-y-3">
            <LoadingSpinner />
            <p className="text-sm text-muted-foreground">
              Processing your Linktree data...
            </p>
          </div>
        )}
        
        {apiResponse && !isLoading && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Response Data:</span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyJson}
                type="button"
              >
                Copy JSON
              </Button>
            </div>
            <pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm border max-h-96 overflow-y-auto">
              {JSON.stringify(apiResponse, null, 2)}
            </pre>
          </div>
        )}
        
        {!isLoading && !apiResponse && (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-2">No data yet</p>
            <p className="text-sm text-muted-foreground">
              The API response will be displayed here after processing.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
