import React from "react";
import { UserProfile } from "@/types/user";
import { transformSocialMediaData } from "@/lib/brokenInkUtils";
import { SectionHeader } from "@/components/ui/section-header";
import { SocialGrid } from "./SocialGrid";

interface SocialMediaSectionProps {
  profile: UserProfile;
}

const SocialMediaSection = ({ profile }: SocialMediaSectionProps) => {
  const socialData = transformSocialMediaData(profile);

  return (
    <section
      id="socialinks"
      className="py-10 bg-black sm:py-20 max-w-7xl mx-auto"
    >
      <div className="container mx-auto px-4 space-y-12">
        <SectionHeader
          title={socialData.title}
          description={socialData.description}
        />

        <SocialGrid platforms={socialData.socialPlatforms} />

        {/* Optional: Add a "View All Links" button if needed */}
        {/* <div className="flex justify-center">
          <button
            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-8 bg-white text-black text-base font-bold leading-normal tracking-wide transition-transform hover:scale-105"
            style={
              socialData.colors?.primary
                ? {
                    backgroundColor: socialData.colors.primary,
                    color: socialData.colors.linkText || "#ffffff",
                  }
                : {}
            }
          >
            <span className="truncate">Ver todos os Links</span>
          </button>
        </div> */}
      </div>
    </section>
  );
};

export default SocialMediaSection;
