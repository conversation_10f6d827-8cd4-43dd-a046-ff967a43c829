"use client";

import React, { useCallback, useEffect, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { ReviewCarouselProps } from "./types";
import { ReviewCard } from "./ReviewCard";
import { NavigationControls } from "../shared/NavigationControls";
import { ProgressIndicator } from "../shared/ProgressIndicator";

export const ReviewCarousel = ({ reviews }: ReviewCarouselProps) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: true,
  });

  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const scrollTo = useCallback(
    (index: number) => {
      if (emblaApi) emblaApi.scrollTo(index);
    },
    [emblaApi]
  );

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    // Initialize state on mount or when emblaApi changes
    onSelect();

    // Subscribe to events
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);

    // Cleanup to avoid memory leaks and duplicate handlers
    return () => {
      // emblaApi might be stale if instance re-initializes; guard anyway
      try {
        emblaApi.off("select", onSelect);
        emblaApi.off("reInit", onSelect);
      } catch {
        // no-op: defensive in case emblaApi has been disposed
      }
    };
  }, [emblaApi, onSelect]);

  return (
    <div className="relative w-full overflow-hidden">
      <div className="overflow-hidden rounded-xl" ref={emblaRef}>
        <div className="flex gap-3 sm:gap-4 lg:gap-6">
          {reviews.map((review, index) => (
            <ReviewCard key={review.id} review={review} index={index} />
          ))}
        </div>
      </div>

      <NavigationControls
        onPrevious={scrollPrev}
        onNext={scrollNext}
        canScrollPrev={canScrollPrev}
        canScrollNext={canScrollNext}
        previousLabel="Avaliação anterior"
        nextLabel="Próxima avaliação"
        itemCount={reviews.length}
      />

      <ProgressIndicator
        count={reviews.length}
        selectedIndex={selectedIndex}
        onSelect={scrollTo}
        ariaLabel="Navigate to review"
      />
    </div>
  );
};
