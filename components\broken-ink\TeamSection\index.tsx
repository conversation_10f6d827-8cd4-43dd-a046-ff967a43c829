"use client";

import React from "react";
import { UserProfile } from "@/types/user";
import { transformTeamData } from "@/lib/brokenInkUtils";
import { useScreenSize } from "@/components/hooks/useScreenSize";
import { SectionHeader } from "@/components/ui/section-header";
import { TeamGrid } from "./TeamGrid";
import { TeamCarousel } from "./TeamCarousel";

interface TeamSectionProps {
  profile: UserProfile;
}

const TeamSection = ({ profile }: TeamSectionProps) => {
  const artistsData = transformTeamData(profile);
  const { isMounted, isLargeScreen } = useScreenSize();

  if (!artistsData.enabled) {
    return null;
  }

  // Check if we should use grid layout (1-3 artists on large screens)
  const shouldUseGrid =
    isMounted && isLargeScreen && artistsData.artists.length <= 3;

  return (
    <section className="py-16 sm:py-24 bg-black mx-auto max-w-7xl" id="artists">
      <div className="px-4 sm:px-6 lg:px-8 flex flex-col items-center">
        <SectionHeader title={artistsData.title} className="mb-12" />

        {/* Adaptive Layout: Grid for 1-3 artists on large screens, Carousel otherwise */}
        {shouldUseGrid ? (
          <TeamGrid artists={artistsData.artists} />
        ) : (
          <TeamCarousel artists={artistsData.artists} />
        )}
      </div>
    </section>
  );
};

export default TeamSection;
