"use client";

import React from "react";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  FormField,
  FormLabel,
  FormDescription,
} from "@/components/ui/form";
import { PREDEFINED_TEMPLATE_TEXT } from "./constants";
import { FileText, Plus, RotateCcw } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface AdditionalInfoFieldProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
}

export default function AdditionalInfoField({
  value,
  onChange,
  disabled = false,
}: AdditionalInfoFieldProps) {
  const handleInsertTemplate = () => {
    onChange(PREDEFINED_TEMPLATE_TEXT);
  };

  const handleClear = () => {
    onChange("");
  };

  const characterCount = value.length;
  const hasContent = value.trim().length > 0;

  return (
    <div className="space-y-4">
      <FormField>
        <FormLabel htmlFor="additional-info" className="flex items-center gap-2">
          <FileText className="w-4 h-4 text-green-500" />
          Additional Information
          <Badge variant="outline" className="text-xs">
            Optional
          </Badge>
        </FormLabel>
        <div className="relative">
          <Textarea
            id="additional-info"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder="Optional additional information about your business for better processing..."
            rows={8}
            className="resize-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500"
            disabled={disabled}
          />
          <div className="absolute bottom-3 right-3 text-xs text-muted-foreground">
            {characterCount} characters
          </div>
        </div>
        <FormDescription>
          Provide any additional context about your business to improve data processing
        </FormDescription>
      </FormField>

      <div className="flex flex-col sm:flex-row gap-2">
        <Button
          onClick={handleInsertTemplate}
          disabled={disabled}
          variant="outline"
          size="sm"
          className="flex-1 hover:bg-green-50 hover:border-green-300 dark:hover:bg-green-950/50"
          type="button"
        >
          <Plus className="w-4 h-4 mr-2" />
          Insert Pre-defined Template
        </Button>
        
        {hasContent && (
          <Button
            onClick={handleClear}
            disabled={disabled}
            variant="ghost"
            size="sm"
            className="hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-950/50"
            type="button"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Clear
          </Button>
        )}
      </div>
    </div>
  );
}