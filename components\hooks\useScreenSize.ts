import { useEffect, useState } from "react";

interface ScreenSizeHook {
    isMounted: boolean;
    isLargeScreen: boolean;
}

export const useScreenSize = (breakpoint: number = 1024): ScreenSizeHook => {
    const [isMounted, setIsMounted] = useState(false);
    const [isLargeScreen, setIsLargeScreen] = useState(false);

    useEffect(() => {
        setIsMounted(true);

        const checkScreenSize = () => {
            setIsLargeScreen(window.innerWidth >= breakpoint);
        };

        // Initial check
        checkScreenSize();

        window.addEventListener("resize", checkScreenSize);
        return () => window.removeEventListener("resize", checkScreenSize);
    }, [breakpoint]);

    return { isMounted, isLargeScreen };
};