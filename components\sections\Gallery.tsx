"use client";

import useEmblaCarousel from "embla-carousel-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { ChevronLeft, ChevronRight, Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { createColorVariations, isValidCSSColor } from "@/lib/colorUtils";

// Import PhotoSwipe styles
import "photoswipe/dist/photoswipe.css";

// Import PhotoSwipe
import PhotoSwipeLightbox from "photoswipe/lightbox";
import PhotoSwipe from "photoswipe";

interface GalleryImage {
  id: number;
  url: string;
  alt: string;
  title: string;
  description: string;
}

interface GalleryProps {
  gallery: {
    title: string;
    description: string;
    enabled: boolean;
    images: GalleryImage[];
  };
  colors?: {
    background?: string;
    linkText?: string;
    primary?: string;
    secondary?: string;
    socialIconBackground?: string;
  };
}

const Gallery = ({ gallery, colors }: GalleryProps) => {
  const galleryRef = useRef<HTMLDivElement>(null);
  const [imageDimensions, setImageDimensions] = useState<{
    [key: string]: { width: number; height: number };
  }>({});
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    loop: true,
  });

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  // Initialize PhotoSwipe
  useEffect(() => {
    let lightbox: PhotoSwipeLightbox | null = null;

    if (
      galleryRef.current &&
      gallery.images.length > 0 &&
      Object.keys(imageDimensions).length > 0
    ) {
      lightbox = new PhotoSwipeLightbox({
        gallery: "#photo-gallery",
        children: "a",
        pswpModule: PhotoSwipe,
        // Options
        showHideAnimationType: "fade",
        showAnimationDuration: 300,
        hideAnimationDuration: 300,
        zoom: true,
        counter: true,
        close: true,
        imageClickAction: "zoom",
        tapAction: "zoom",
      });

      lightbox.init();
    }

    return () => {
      if (lightbox) {
        lightbox.destroy();
      }
    };
  }, [gallery.images, imageDimensions]);

  // Preload images and get their real dimensions
  useEffect(() => {
    const loadImageDimensions = async () => {
      const dimensions: { [key: string]: { width: number; height: number } } =
        {};

      await Promise.all(
        gallery.images.map((image) => {
          return new Promise<void>((resolve) => {
            const img = new window.Image();
            img.onload = () => {
              dimensions[image.url] = {
                width: img.naturalWidth,
                height: img.naturalHeight,
              };
              resolve();
            };
            img.onerror = () => {
              // Fallback dimensions if image fails to load
              dimensions[image.url] = {
                width: 1200,
                height: 800,
              };
              resolve();
            };
            img.src = image.url;
          });
        })
      );

      setImageDimensions(dimensions);
    };

    if (gallery.images.length > 0) {
      loadImageDimensions();
    }
  }, [gallery.images]);

  if (!gallery.enabled) return null;

  return (
    <section className="mb-12 animate-fade-in">
      <div className="text-center mb-8 lg:mb-12">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground mb-3">
          {gallery.title}
        </h2>
        <p className="text-muted-foreground max-w-md lg:max-w-2xl mx-auto text-sm sm:text-base leading-relaxed">
          {gallery.description}
        </p>
      </div>

      <div className="relative" ref={galleryRef} id="photo-gallery">
        <div
          className="overflow-hidden rounded-xl sm:rounded-2xl"
          ref={emblaRef}
        >
          <div className="flex">
            {gallery.images.map((image, index) => (
              <div
                key={image.id}
                className="flex-[0_0_100%] min-w-0 px-1 sm:px-2"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.02] transform-gpu group rounded-lg">
                  <a
                    href={image.url}
                    data-pswp-width={imageDimensions[image.url]?.width || 1200}
                    data-pswp-height={imageDimensions[image.url]?.height || 800}
                    data-pswp-caption={`<h4>${image.title}</h4><p>${image.description}</p>`}
                    target="_blank"
                    rel="noreferrer"
                    className="block relative group cursor-pointer"
                  >
                    <Image
                      src={image.url}
                      alt={image.alt}
                      width={imageDimensions[image.url]?.width || 1200}
                      height={imageDimensions[image.url]?.height || 800}
                      className="w-full h-64 sm:h-80 lg:h-96 object-cover transition-transform duration-700 group-hover:scale-110"
                      priority={index === 0}
                      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 100vw"
                    />
                    {/* Enhanced overlay */}
                    <div className="absolute inset-0 bg-linear-to-t from-black/90 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-end">
                      <div className="p-4 sm:p-6 lg:p-8 text-white w-full transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                        <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-2 drop-shadow-lg">
                          {image.title}
                        </h3>
                        <p className="text-sm sm:text-base opacity-90 leading-relaxed drop-shadow-sm">
                          {image.description}
                        </p>
                      </div>
                    </div>

                    {/* View icon overlay */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
                        <Eye
                          className="w-6 h-6 text-white"
                          aria-hidden="true"
                        />
                      </div>
                    </div>

                    {/* Image counter badge */}
                    <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm rounded-full px-3 py-1">
                      <span className="text-white text-xs sm:text-sm font-medium">
                        {index + 1} / {gallery.images.length}
                      </span>
                    </div>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Enhanced navigation buttons */}
        <div className="flex justify-center mt-6 sm:mt-8 gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={scrollPrev}
            className="w-10 h-10 sm:w-12 sm:h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-200 hover:scale-110 transform"
            style={
              colors?.primary && isValidCSSColor(colors.primary)
                ? (() => {
                    const colorVariations = createColorVariations(
                      colors.primary
                    );
                    return {
                      backgroundColor: `${colorVariations.base}CC`, // 80% opacity
                      color: colors.linkText || "#ffffff",
                      borderColor: "transparent",
                    } as React.CSSProperties;
                  })()
                : {}
            }
            onMouseEnter={(e) => {
              if (colors?.primary && isValidCSSColor(colors.primary)) {
                const colorVariations = createColorVariations(colors.primary);
                e.currentTarget.style.backgroundColor = colorVariations.base;
              }
            }}
            onMouseLeave={(e) => {
              if (colors?.primary && isValidCSSColor(colors.primary)) {
                const colorVariations = createColorVariations(colors.primary);
                e.currentTarget.style.backgroundColor = `${colorVariations.base}CC`;
              }
            }}
            aria-label="Imagem anterior"
          >
            <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={scrollNext}
            className="w-10 h-10 sm:w-12 sm:h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-200 hover:scale-110 transform"
            style={
              colors?.primary && isValidCSSColor(colors.primary)
                ? (() => {
                    const colorVariations = createColorVariations(
                      colors.primary
                    );
                    return {
                      backgroundColor: `${colorVariations.base}CC`, // 80% opacity
                      color: colors.linkText || "#ffffff",
                      borderColor: "transparent",
                    } as React.CSSProperties;
                  })()
                : {}
            }
            onMouseEnter={(e) => {
              if (colors?.primary && isValidCSSColor(colors.primary)) {
                const colorVariations = createColorVariations(colors.primary);
                e.currentTarget.style.backgroundColor = colorVariations.base;
              }
            }}
            onMouseLeave={(e) => {
              if (colors?.primary && isValidCSSColor(colors.primary)) {
                const colorVariations = createColorVariations(colors.primary);
                e.currentTarget.style.backgroundColor = `${colorVariations.base}CC`;
              }
            }}
            aria-label="Próxima imagem"
          >
            <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5" />
          </Button>
        </div>

        {/* Dots indicator */}
        <div className="flex justify-center mt-4 gap-2">
          {gallery.images.map((_, index) => (
            <div
              key={index}
              className="w-2 h-2 rounded-full transition-all duration-300 cursor-pointer"
              style={{
                backgroundColor:
                  colors?.primary && isValidCSSColor(colors.primary)
                    ? colors.primary
                    : undefined,
              }}
              onClick={() => emblaApi?.scrollTo(index)}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Gallery;
