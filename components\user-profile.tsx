"use client";

import React from "react";
import { UserProfile as UserProfileType } from "@/types/user";
import { cn } from "@/lib/utils";
import { ThemeToggle } from "./theme-toggle";
import { getButtonConfig } from "@/lib/buttonUtils";

// Import section components
import UserProfileSection from "./sections/UserProfile";
import SocialMedia from "./sections/SocialMedia";
import LinksSection from "./sections/LinksSection";
import Gallery from "./sections/Gallery";
import Reviews from "./sections/Reviews";
import GenericSection from "./sections/GenericSection";
import Location from "./sections/Location";

interface UserProfileProps {
  profile: UserProfileType;
  className?: string;
}

export function UserProfile({ profile, className }: UserProfileProps) {
  // Use the correct Firebase data structure
  const links = profile.links || [];
  const socialLinks = profile.socialMedia || [];
  const colors = profile.settings?.colors;

  const containerStyle = {
    // backgroundColor: colors?.background || undefined,
    color: colors?.linkText || undefined,
  };

  return (
    <div
      className={cn(
        "min-h-screen w-full bg-background text-foreground relative",
        className
      )}
      style={containerStyle}
    >
      {/* Floating Theme Toggle */}
      <div className="fixed top-4 right-4 z-50">
        <ThemeToggle />
      </div>

      <div className="mx-auto max-w-full px-4 py-8 flex flex-col items-center">
        {/* Profile Header Section */}
        {profile.user && (
          <section
            className="max-w-xl mx-auto mb-8"
            aria-label="Profile Header"
          >
            <UserProfileSection user={profile.user} colors={colors} />
          </section>
        )}

        {/* Social Media Section */}
        {socialLinks && socialLinks.length > 0 && (
          <section
            className="max-w-xl mx-auto mb-8"
            aria-label="Social Media Links"
          >
            <SocialMedia
              socialMedia={socialLinks}
              size="md"
              variant="default"
              colors={colors}
            />
          </section>
        )}

        {/* Links Section */}
        {links && links.length > 0 && (
          <section
            className="max-w-xl mx-auto mb-8"
            aria-label="Navigation Links"
          >
            <LinksSection links={links} colors={colors} />
          </section>
        )}

        {/* Gallery Section */}
        {profile.gallery && profile.gallery.enabled && (
          <section className="max-w-xl mx-auto mb-8" aria-label="Photo Gallery">
            <Gallery gallery={profile.gallery} colors={colors} />
          </section>
        )}

        {/* Features Section */}
        {profile.featuresSection && profile.featuresSection.enabled && (
          <section
            className="max-w-full mx-auto mb-8 text-center"
            aria-label="Features Showcase"
          >
            <GenericSection
              sectionData={profile.featuresSection}
              layout="carousel"
              {...getButtonConfig(
                "features",
                profile.featuresSection.buttonConfig
              )}
              sectionType="features"
              colors={colors}
            />
          </section>
        )}

        {/* Services Section */}
        {profile.servicesSection && profile.servicesSection.enabled && (
          <section
            className="max-w-full mx-auto mb-8 text-center"
            aria-label="Services Offered"
          >
            <GenericSection
              sectionData={profile.servicesSection}
              layout="carousel"
              {...getButtonConfig(
                "services",
                profile.servicesSection.buttonConfig
              )}
              sectionType="services"
              colors={colors}
            />
          </section>
        )}

        {/* Generic Content Section */}
        {profile.genericSection && profile.genericSection.enabled && (
          <section
            className="max-w-full mx-auto mb-8 text-center"
            aria-label="Additional Content"
          >
            <GenericSection
              sectionData={profile.genericSection}
              layout="carousel"
              {...getButtonConfig(
                "generic",
                profile.genericSection.buttonConfig
              )}
              sectionType="generic"
              colors={colors}
            />
          </section>
        )}

        {/* Reviews Section */}
        {profile.reviews && profile.reviews.enabled && (
          <section
            className="max-w-full mx-auto mb-8"
            aria-label="Customer Reviews"
          >
            <Reviews reviews={profile.reviews} colors={colors} />
          </section>
        )}

        {/* Location Section - if location data exists */}
        {profile.location && profile.location.enabled && (
          <section
            className="max-w-full mx-auto mb-8"
            aria-label="Location Information"
          >
            <Location
              title="Nossa Localização"
              description="Venha nos visitar em nosso estabelecimento"
              coordinates={{ lat: -23.5505, lng: -46.6333 }}
              locationData={profile.location}
            />
          </section>
        )}

        {/* Footer Section */}
        <section className="max-w-xl mx-auto" aria-label="Footer">
          <div className="text-center mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
            <p className="text-xs opacity-50">Created with AvencaLink</p>
          </div>
        </section>
      </div>
    </div>
  );
}
