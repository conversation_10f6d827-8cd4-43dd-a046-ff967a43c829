"use client";

import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FormField, FormLabel, FormDescription } from "@/components/ui/form";
import { templateOptions } from "./constants";
import { TemplateOption } from "./types";
import { FileText, Sparkles } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface TemplateSelectorProps {
  selectedTemplate: string;
  onTemplateChange: (template: string) => void;
  disabled?: boolean;
}

export default function TemplateSelector({
  selectedTemplate,
  onTemplateChange,
  disabled = false,
}: TemplateSelectorProps) {
  const selectedOption = templateOptions.find(t => t.value === selectedTemplate);

  return (
    <FormField>
      <FormLabel htmlFor="template-select" className="flex items-center gap-2">
        <FileText className="w-4 h-4 text-purple-500" />
        Processing Template
      </FormLabel>
      <Select
        value={selectedTemplate}
        onValueChange={onTemplateChange}
        disabled={disabled}
      >
        <SelectTrigger 
          id="template-select"
          className="h-12 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500"
        >
          <SelectValue placeholder="Select a template">
            {selectedOption && (
              <div className="flex items-center gap-2">
                <span className="font-medium">{selectedOption.label}</span>
                {selectedOption.value !== 'general' && (
                  <Badge variant="secondary" className="text-xs">
                    <Sparkles className="w-3 h-3 mr-1" />
                    Specialized
                  </Badge>
                )}
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {templateOptions.map((template: TemplateOption) => (
            <SelectItem key={template.value} value={template.value} className="py-3">
              <div className="flex flex-col gap-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{template.label}</span>
                  {template.value !== 'general' && (
                    <Badge variant="outline" className="text-xs">
                      Specialized
                    </Badge>
                  )}
                </div>
                <span className="text-xs text-muted-foreground">
                  {template.description}
                </span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <FormDescription>
        Choose the template that best matches your business type for optimized AI processing
      </FormDescription>
    </FormField>
  );
}