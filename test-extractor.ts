// Test script for linktreeExtractor.ts
// Run with: npx tsx test-extractor.ts
// Or compile and run: npx tsc test-extractor.ts && node test-extractor.js

import { extractLinktreeData } from './lib/linktreeExtractor';

async function testExtractor() {
    console.log('🚀 Testing Linktree Extractor...\n');

    const testUrl = 'https://linktr.ee/chale_barbearia';

    try {
        console.log(`📡 Extracting data from: ${testUrl}`);
        console.log('⏳ Please wait...\n');

        const startTime = Date.now();
        const result = await extractLinktreeData(testUrl);
        const endTime = Date.now();

        console.log(`✅ Extraction completed in ${endTime - startTime}ms\n`);
        console.log('📊 Full Result:');
        console.log('================');
        console.log(JSON.stringify(result, null, 2));

        console.log('\n📋 Analysis:');
        console.log('=============');
        console.log(`Success: ${result.success}`);

        if (result.success && result.data) {
            console.log('\n🔗 Extracted Data:');
            console.log(`  Links: ${result.data.links ? result.data.links.length : 0}`);

            if (result.data.extra_info) {
                console.log(`  Username: ${result.data.extra_info.username || 'N/A'}`);
                console.log(`  Description: ${result.data.extra_info.description || 'N/A'}`);
                console.log(`  Location: ${result.data.extra_info.location || 'N/A'}`);
                console.log(`  Avatar URL: ${result.data.extra_info.avatar_url || 'N/A'}`);
                console.log(`  Bio: ${result.data.extra_info.user_bio || 'N/A'}`);
            }

            if (result.data.links && result.data.links.length > 0) {
                console.log('  Links found:');
                result.data.links.forEach((link, linkIndex: number) => {
                    console.log(`    ${linkIndex + 1}. ${link.title || 'Untitled'}: ${link.url}`);
                });
            }
        } else {
            console.log('⚠️  No data extracted - this could indicate:');
            console.log('   - The page structure doesn\'t match our schema');
            console.log('   - The page requires JavaScript rendering');
            console.log('   - The FireCrawl API couldn\'t access the content');
            console.log('   - The prompt needs adjustment');
            console.log('   - Rate limiting or API issues');
        }

        if (result.error) {
            console.log(`\n❌ Error: ${result.error}`);
        }

        // Test with different URLs
        console.log('\n🔄 Testing with a different URL...');
        const testUrl2 = 'https://linktr.ee/barbeariatarantino';
        try {
            const result2 = await extractLinktreeData(testUrl2);
            console.log(`\n📊 Result for ${testUrl2}:`);
            console.log(`Success: ${result2.success}`);
            if (result2.success && result2.data) {
                console.log(`Data links: ${result2.data.links ? result2.data.links.length : 0}`);
            }
        } catch (error2) {
            const err = error2 as Error;
            console.log(`❌ Second test failed: ${err.message}`);
        }

    } catch (error) {
        const err = error as Error;
        console.error('💥 Test failed with error:');
        console.error(err.message);
        console.error('\nFull error:', err);

        // Additional debugging info
        if (error && typeof error === 'object' && 'response' in error) {
            const errorWithResponse = error as { response: { status: number; data: unknown } };
            console.error('Response status:', errorWithResponse.response.status);
            console.error('Response data:', errorWithResponse.response.data);
        }
    }
}

// Run the test
console.log('Starting Linktree Extractor Test...');
testExtractor().catch(console.error);