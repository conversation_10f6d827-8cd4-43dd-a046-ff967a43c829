import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const skeletonVariants = cva(
  "animate-pulse rounded-md bg-muted",
  {
    variants: {
      variant: {
        default: "bg-muted",
        shimmer: "bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%] animate-[shimmer_2s_infinite]",
        pulse: "bg-muted animate-pulse",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface SkeletonProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof skeletonVariants> {}

function Skeleton({ className, variant, ...props }: SkeletonProps) {
  return (
    <div
      className={cn(skeletonVariants({ variant }), className)}
      {...props}
    />
  )
}

// Predefined skeleton components for common use cases
interface SkeletonTextProps {
  lines?: number
  className?: string
  variant?: VariantProps<typeof skeletonVariants>['variant']
}

function SkeletonText({ lines = 1, className, variant }: SkeletonTextProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton
          key={i}
          variant={variant}
          className={cn(
            "h-4",
            i === lines - 1 && lines > 1 ? "w-3/4" : "w-full"
          )}
        />
      ))}
    </div>
  )
}

interface SkeletonAvatarProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  variant?: VariantProps<typeof skeletonVariants>['variant']
}

function SkeletonAvatar({ size = 'md', className, variant }: SkeletonAvatarProps) {
  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-10 w-10",
    lg: "h-12 w-12",
    xl: "h-16 w-16",
  }

  return (
    <Skeleton
      variant={variant}
      className={cn("rounded-full", sizeClasses[size], className)}
    />
  )
}

interface SkeletonButtonProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
  variant?: VariantProps<typeof skeletonVariants>['variant']
}

function SkeletonButton({ size = 'md', className, variant }: SkeletonButtonProps) {
  const sizeClasses = {
    sm: "h-8 w-20",
    md: "h-10 w-24",
    lg: "h-12 w-28",
  }

  return (
    <Skeleton
      variant={variant}
      className={cn("rounded-md", sizeClasses[size], className)}
    />
  )
}

interface SkeletonCardProps {
  className?: string
  variant?: VariantProps<typeof skeletonVariants>['variant']
  showAvatar?: boolean
  showImage?: boolean
  textLines?: number
}

function SkeletonCard({ 
  className, 
  variant, 
  showAvatar = false, 
  showImage = false, 
  textLines = 3 
}: SkeletonCardProps) {
  return (
    <div className={cn("space-y-4 p-4 border rounded-lg", className)}>
      {showImage && (
        <Skeleton variant={variant} className="h-48 w-full rounded-md" />
      )}
      
      <div className="space-y-3">
        {showAvatar && (
          <div className="flex items-center space-x-3">
            <SkeletonAvatar variant={variant} />
            <div className="space-y-2 flex-1">
              <Skeleton variant={variant} className="h-4 w-1/3" />
              <Skeleton variant={variant} className="h-3 w-1/4" />
            </div>
          </div>
        )}
        
        <div className="space-y-2">
          <Skeleton variant={variant} className="h-5 w-2/3" />
          <SkeletonText lines={textLines} variant={variant} />
        </div>
        
        <div className="flex space-x-2">
          <SkeletonButton size="sm" variant={variant} />
          <SkeletonButton size="sm" variant={variant} />
        </div>
      </div>
    </div>
  )
}

interface SkeletonListProps {
  items?: number
  className?: string
  variant?: VariantProps<typeof skeletonVariants>['variant']
  showAvatar?: boolean
}

function SkeletonList({ 
  items = 3, 
  className, 
  variant, 
  showAvatar = true 
}: SkeletonListProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3">
          {showAvatar && <SkeletonAvatar variant={variant} />}
          <div className="space-y-2 flex-1">
            <Skeleton variant={variant} className="h-4 w-3/4" />
            <Skeleton variant={variant} className="h-3 w-1/2" />
          </div>
        </div>
      ))}
    </div>
  )
}

interface SkeletonTableProps {
  rows?: number
  columns?: number
  className?: string
  variant?: VariantProps<typeof skeletonVariants>['variant']
}

function SkeletonTable({ 
  rows = 5, 
  columns = 4, 
  className, 
  variant 
}: SkeletonTableProps) {
  return (
    <div className={cn("space-y-3", className)}>
      {/* Header */}
      <div className="flex space-x-4">
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton key={i} variant={variant} className="h-4 flex-1" />
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton 
              key={colIndex} 
              variant={variant} 
              className={cn(
                "h-4 flex-1",
                colIndex === 0 && "w-1/4",
                colIndex === columns - 1 && "w-1/6"
              )} 
            />
          ))}
        </div>
      ))}
    </div>
  )
}

// Add shimmer animation to global styles
const shimmerKeyframes = `
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`

// Inject styles if shimmer variant is used
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = shimmerKeyframes
  document.head.appendChild(style)
}

export {
  Skeleton,
  SkeletonText,
  SkeletonAvatar,
  SkeletonButton,
  SkeletonCard,
  SkeletonList,
  SkeletonTable,
}
