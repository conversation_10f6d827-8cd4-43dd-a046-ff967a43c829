import React from "react";
import { LoadingCard } from "./LoadingCard";

interface LinkCardProps {
  icon: React.ReactNode;
  title: string;
  description?: string;
  onClick: (event: React.MouseEvent) => void;
  isLoading?: boolean;
}

export const LinkCard: React.FC<LinkCardProps> = ({
  icon,
  title,
  onClick,
  isLoading = false,
}) => {
  if (isLoading) {
    return <LoadingCard />;
  }

  return (
    <button
      className="group relative flex items-center justify-between rounded-3xl bg-custom backdrop-blur-3xl w-full p-6 ring-1 ring-black/10 transition-all hover:ring-white/20 hover:scale-[1.02] active:scale-[0.98] text-left"
      onClick={onClick}
    >
      <div className="flex items-center gap-4 w-full">
        <div className="bg-black/40 p-4 rounded-3xl transition-all group-hover:bg-black/60 flex-shrink-0">
          {icon || <div className="w-6 h-6" />}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-white text-lg font-semibold mb-1">{title}</h3>
        </div>
      </div>
    </button>
  );
};
