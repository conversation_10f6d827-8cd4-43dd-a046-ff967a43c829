import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const navigationVariants = cva(
  "flex items-center",
  {
    variants: {
      orientation: {
        horizontal: "flex-row space-x-1",
        vertical: "flex-col space-y-1",
      },
      variant: {
        default: "",
        pills: "bg-muted p-1 rounded-lg",
        underline: "border-b border-border",
        sidebar: "w-full space-y-1",
      },
    },
    defaultVariants: {
      orientation: "horizontal",
      variant: "default",
    },
  }
)

const navigationItemVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "hover:bg-accent hover:text-accent-foreground data-[active=true]:bg-accent data-[active=true]:text-accent-foreground",
        pills: "hover:bg-background hover:text-foreground data-[active=true]:bg-background data-[active=true]:text-foreground data-[active=true]:shadow-sm",
        underline: "hover:text-foreground data-[active=true]:text-foreground data-[active=true]:border-b-2 data-[active=true]:border-primary rounded-none border-b-2 border-transparent",
        sidebar: "w-full justify-start hover:bg-accent hover:text-accent-foreground data-[active=true]:bg-accent data-[active=true]:text-accent-foreground",
        ghost: "hover:bg-accent hover:text-accent-foreground data-[active=true]:bg-accent data-[active=true]:text-accent-foreground",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 px-3 text-sm",
        lg: "h-10 px-6",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface NavigationProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof navigationVariants> {
  children: React.ReactNode
}

const Navigation = React.forwardRef<HTMLElement, NavigationProps>(
  ({ className, orientation, variant, children, ...props }, ref) => {
    return (
      <nav
        ref={ref}
        className={cn(navigationVariants({ orientation, variant }), className)}
        {...props}
      >
        {children}
      </nav>
    )
  }
)

Navigation.displayName = "Navigation"

export interface NavigationItemProps
  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, 'href'>,
    VariantProps<typeof navigationItemVariants> {
  href?: string
  active?: boolean
  disabled?: boolean
  icon?: React.ReactNode
  badge?: React.ReactNode
  external?: boolean
}

const NavigationItem = React.forwardRef<HTMLAnchorElement, NavigationItemProps>(
  ({ 
    className, 
    variant, 
    size, 
    href, 
    active, 
    disabled, 
    icon, 
    badge, 
    external = false,
    children, 
    ...props 
  }, ref) => {
    const pathname = usePathname()
    const isActive = active ?? (href ? pathname === href : false)

    const content = (
      <>
        {icon && <span className="mr-2">{icon}</span>}
        {children}
        {badge && <span className="ml-auto">{badge}</span>}
      </>
    )

    if (!href || disabled) {
      return (
        <span
          ref={ref as React.Ref<HTMLSpanElement>}
          className={cn(
            navigationItemVariants({ variant, size }),
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          data-active={isActive}
          aria-disabled={disabled}
          {...props}
        >
          {content}
        </span>
      )
    }

    if (external) {
      return (
        <a
          ref={ref}
          href={href}
          className={cn(navigationItemVariants({ variant, size }), className)}
          data-active={isActive}
          target="_blank"
          rel="noopener noreferrer"
          {...props}
        >
          {content}
        </a>
      )
    }

    return (
      <Link
        ref={ref}
        href={href}
        className={cn(navigationItemVariants({ variant, size }), className)}
        data-active={isActive}
        {...props}
      >
        {content}
      </Link>
    )
  }
)

NavigationItem.displayName = "NavigationItem"

interface NavigationGroupProps {
  children: React.ReactNode
  label?: string
  className?: string
}

const NavigationGroup = React.forwardRef<HTMLDivElement, NavigationGroupProps>(
  ({ children, label, className, ...props }, ref) => {
    return (
      <div ref={ref} className={cn("space-y-2", className)} {...props}>
        {label && (
          <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            {label}
          </div>
        )}
        <div className="space-y-1">
          {children}
        </div>
      </div>
    )
  }
)

NavigationGroup.displayName = "NavigationGroup"

interface BreadcrumbProps {
  children: React.ReactNode
  className?: string
  separator?: React.ReactNode
}

const Breadcrumb = React.forwardRef<HTMLElement, BreadcrumbProps>(
  ({ children, className, separator, ...props }, ref) => {
    const defaultSeparator = (
      <svg className="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
      </svg>
    )

    const childrenArray = React.Children.toArray(children)

    return (
      <nav
        ref={ref}
        className={cn("flex items-center space-x-2 text-sm", className)}
        aria-label="Breadcrumb"
        {...props}
      >
        {childrenArray.map((child, index) => (
          <React.Fragment key={index}>
            {child}
            {index < childrenArray.length - 1 && (
              <span aria-hidden="true">
                {separator || defaultSeparator}
              </span>
            )}
          </React.Fragment>
        ))}
      </nav>
    )
  }
)

Breadcrumb.displayName = "Breadcrumb"

interface BreadcrumbItemProps {
  children: React.ReactNode
  href?: string
  active?: boolean
  className?: string
}

const BreadcrumbItem = React.forwardRef<HTMLAnchorElement, BreadcrumbItemProps>(
  ({ children, href, active = false, className, ...props }, ref) => {
    if (!href || active) {
      return (
        <span
          ref={ref as React.Ref<HTMLSpanElement>}
          className={cn(
            "text-muted-foreground",
            active && "text-foreground font-medium",
            className
          )}
          aria-current={active ? "page" : undefined}
          {...props}
        >
          {children}
        </span>
      )
    }

    return (
      <Link
        ref={ref}
        href={href}
        className={cn("text-muted-foreground hover:text-foreground transition-colors", className)}
        {...props}
      >
        {children}
      </Link>
    )
  }
)

BreadcrumbItem.displayName = "BreadcrumbItem"

export {
  Navigation,
  NavigationItem,
  NavigationGroup,
  Breadcrumb,
  BreadcrumbItem,
  navigationVariants,
  navigationItemVariants,
}
