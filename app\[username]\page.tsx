import { Metadata } from "next";
import { notFound } from "next/navigation";
import { BrokenInkProfileClient } from "./profile-client";
import { generateMetaTitle, generateMetaDescription } from "@/lib/utils";
import {
  UserProfile,
  UserNotFoundError,
  InvalidUsernameError,
} from "@/types/user";
import { apiService } from "@/services/api";

interface UserPageProps {
  params: Promise<{ username: string }>;
}

// Fetch user profile from API
async function getUserProfile(username: string): Promise<UserProfile | null> {
  try {
    const profile = await apiService.getUserProfile(username);
    return profile;
  } catch (error) {
    if (
      error instanceof UserNotFoundError ||
      error instanceof InvalidUsernameError
    ) {
      return null;
    }
    console.error("Error fetching user profile:", error);
    return null;
  }
}

export async function generateMetadata({
  params,
}: UserPageProps): Promise<Metadata> {
  const { username } = await params;

  try {
    const profile = await getUserProfile(username);

    if (!profile) {
      return {
        title: "User Not Found | AvencaLink",
        description: "The requested user profile could not be found.",
      };
    }

    const title = generateMetaTitle(profile.user.username, profile.user.name);
    const description = generateMetaDescription(
      profile.user.bio,
      profile.user.username
    );
    const profileUrl = `${
      process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"
    }/${username}`;

    return {
      title,
      description,
      openGraph: {
        title,
        description,
        url: profileUrl,
        siteName: "AvencaLink",
        images: profile.user.avatar
          ? [
              {
                url: profile.user.avatar,
                width: 400,
                height: 400,
                alt: `${profile.user.name}'s profile picture`,
              },
            ]
          : [],
        type: "profile",
      },
      twitter: {
        card: "summary",
        title,
        description,
        images: profile.user.avatar ? [profile.user.avatar] : [],
      },
      alternates: {
        canonical: profileUrl,
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Página de Links",
      description: "Create your link-in-bio page",
    };
  }
}

export default async function UserPage({ params }: UserPageProps) {
  const { username } = await params;

  try {
    const profile = await getUserProfile(username);

    if (!profile) {
      notFound();
    }

    return <BrokenInkProfileClient initialProfile={profile} />;
  } catch (error) {
    console.error("Error loading user profile:", error);
    notFound();
  }
}
