import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatUrl(url: string): string {
  if (!url) return ""

  // Add https:// if no protocol is specified
  if (!url.startsWith("http://") && !url.startsWith("https://")) {
    return `https://${url}`
  }

  return url
}

export function validateUsername(username: string): boolean {
  // Username validation: alphanumeric, underscores, hyphens, 3-30 characters
  const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/
  return usernameRegex.test(username)
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + "..."
}

export function generateMetaTitle(username: string, displayName?: string): string {
  const name = displayName || username
  return `${name} | Demonstração`
}

export function generateMetaDescription(bio?: string, username?: string): string {
  if (bio) {
    return truncateText(bio, 160)
  }
  return `Check out ${username}'s links on AvencaLink`
}
