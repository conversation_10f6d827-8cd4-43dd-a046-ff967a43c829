import { SectionItem } from "@/types/user";

export interface SectionCarouselProps {
    items: SectionItem[];
    sectionType: "generic" | "features";
    buttonConfig: {
        primaryButtonText: string;
        secondaryButtonText: string;
        showBadge: boolean;
    };
    onButtonClick: (url: string, event?: React.MouseEvent) => void;
}

export interface SectionCardProps {
    item: SectionItem;
    sectionType: "generic" | "features";
    buttonConfig: {
        primaryButtonText: string;
        secondaryButtonText: string;
        showBadge: boolean;
    };
    onButtonClick: (url: string, event?: React.MouseEvent) => void;
}

export interface CarouselNavigationControlsProps {
    onPrevious: () => void;
    onNext: () => void;
    canScrollPrev: boolean;
    canScrollNext: boolean;
    previousLabel: string;
    nextLabel: string;
}

export interface CarouselProgressIndicatorProps {
    items: SectionItem[];
    selectedIndex: number;
    onSelect: (index: number) => void;
}