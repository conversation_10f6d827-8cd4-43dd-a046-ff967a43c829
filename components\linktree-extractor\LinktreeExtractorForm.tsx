"use client";

import React, { useEffect, useMemo, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import TemplateSelector from "./TemplateSelector";
import UrlInputField from "./UrlInputField";
import AdditionalIn<PERSON>Field from "./AdditionalInfoField";
import AutoExtractSwitch from "./AutoExtractSwitch";
import ActionButtons from "./ActionButtons";
import FormHeader from "./components/FormHeader";
import ErrorAlert from "./components/ErrorAlert";
import QuickActions from "./components/QuickActions";
import { useLinktreeExtractor } from "./hooks/useLinktreeExtractor";

export default function LinktreeExtractorForm() {
  const {
    formData,
    updateFormData,
    isLoading,
    error,
    validationErrors,
    clearValidationError,
    handleExtract,
    handleSubmit,
  } = useLinktreeExtractor();

  const canExtract = useMemo(
    () => formData.linktreeUrl.trim().length > 0,
    [formData.linktreeUrl]
  );

  // Focus the URL field on first render to speed up workflow
  const firstFieldRef = useRef<HTMLInputElement>(null!);
  useEffect(() => {
    firstFieldRef.current?.focus();
  }, []);

  // Submit on Enter when focus is within the form
  const onKeyDown: React.KeyboardEventHandler<HTMLFormElement> = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (!isLoading) handleSubmit();
    }
    if (e.key === "Escape" && error) {
      e.preventDefault();
      // Clear all visible validation errors on ESC for quick reset UX
      Object.keys(validationErrors ?? {}).forEach((k) =>
        clearValidationError(k)
      );
    }
  };

  const handleOpenGPlaces = () => {
    window.open(
      "https://side-projects-gplaces.vhhb1z.easypanel.host/",
      "_blank",
      "noopener,noreferrer"
    );
  };

  const clearError = () => {
    // Clear error by clearing all validation errors
    Object.keys(validationErrors ?? {}).forEach((k) => clearValidationError(k));
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Main Form Card */}
      <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
        <div className="p-6 pb-2">
          <FormHeader />
        </div>

        <CardContent className="pt-4 space-y-6">
          {/* Error Alert */}
          {error && (
            <ErrorAlert error={error} onDismiss={clearError} className="mb-6" />
          )}

          {/* Quick Actions */}
          <QuickActions
            onOpenGPlaces={handleOpenGPlaces}
            disabled={isLoading}
            className="mb-6"
          />

          <Separator />

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Form */}
            <div className="lg:col-span-3 space-y-6">
              <Form
                className="space-y-6"
                onKeyDown={onKeyDown}
                aria-describedby="form-description"
              >
                <p id="form-description" className="sr-only">
                  Formulário para configurar extração do Linktree.
                </p>

                <UrlInputField
                  inputRef={firstFieldRef}
                  value={formData.linktreeUrl}
                  onChange={(value) => updateFormData({ linktreeUrl: value })}
                  validationErrors={validationErrors}
                  onValidationErrorClear={clearValidationError}
                  disabled={isLoading}
                />

                <TemplateSelector
                  selectedTemplate={formData.selectedTemplate}
                  onTemplateChange={(template) =>
                    updateFormData({ selectedTemplate: template })
                  }
                  disabled={isLoading}
                />

                <AutoExtractSwitch
                  checked={formData.autoExtract}
                  onChange={(checked) =>
                    updateFormData({ autoExtract: checked })
                  }
                  disabled={isLoading}
                />

                <AdditionalInfoField
                  value={formData.additionalInfo}
                  onChange={(value) =>
                    updateFormData({ additionalInfo: value })
                  }
                  disabled={isLoading}
                />

                <ActionButtons
                  onExtract={handleExtract}
                  onSubmit={handleSubmit}
                  isLoading={isLoading}
                  canExtract={canExtract}
                  disabled={isLoading}
                />
              </Form>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
