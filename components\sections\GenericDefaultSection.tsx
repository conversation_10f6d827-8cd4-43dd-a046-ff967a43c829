import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import useEmblaCarousel from "embla-carousel-react";
import { useCallback, useEffect, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";

interface SectionItem {
  id: number;
  title: string;
  description: string;
  image: string;
  primaryButton: {
    icon: string;
    url: string;
  };
  secondaryButton: {
    icon: string;
    url: string;
  };
}

interface SectionData {
  title: string;
  description: string;
  enabled: boolean;
  items: SectionItem[];
}

interface GenericSectionProps {
  sectionData: SectionData;
  layout?: "grid" | "carousel";
  primaryButtonText?: string;
  secondaryButtonText?: string;
  showBadge?: boolean;
  sectionType?: "features" | "services" | "generic";
}

const GenericCardSection = ({
  sectionData,
  layout = "grid",
  showBadge = false,
  sectionType = "generic",
}: GenericSectionProps) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: true,
  });

  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);

    return () => {
      try {
        emblaApi.off("select", onSelect);
        emblaApi.off("reInit", onSelect);
      } catch {
        // no-op if emblaApi instance was disposed
      }
    };
  }, [emblaApi, onSelect]);

  if (!sectionData.enabled) return null;

  const renderCard = (item: SectionItem, _index: number) => (
    <Card
      key={item.id}
      className="overflow-hidden shadow-soft hover:shadow-strong transition-all duration-500 hover:scale-[1.02] transform-gpu group h-full"
    >
      <CardContent className="p-0 h-full flex flex-col">
        <div className="relative overflow-hidden">
          <Image
            src={item.image}
            alt={item.title}
            width={400}
            height={224}
            className="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
            loading="lazy"
          />
          <div className="absolute inset-0 bg-linear-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>
          <div className="absolute bottom-4 left-4 right-4 transform transition-transform duration-300 group-hover:translate-y-[-2px]">
            <h3 className="text-white font-bold text-xl mb-1 drop-shadow-lg">
              {item.title}
            </h3>
          </div>

          {/* Optional badge for services */}
          {showBadge && (
            <div className="absolute top-4 right-4">
              <div className="w-8 h-8 bg-primary/90 backdrop-blur-xs rounded-full flex items-center justify-center transform transition-all duration-300 group-hover:scale-110">
                <span className="text-primary-foreground text-sm font-bold">
                  {item.id}
                </span>
              </div>
            </div>
          )}
        </div>
        <div className="p-6 flex-1 flex flex-col">
          <p className="text-muted-foreground text-sm mb-6 leading-relaxed flex-1">
            {item.description}
          </p>
          {/* <div className="flex gap-3">
            <Button
              variant="default"
              size="sm"
              className="flex-1 bg-gradient-primary hover:scale-105 transform transition-all duration-300 ease-spring shadow-soft hover:shadow-medium text-primary-foreground group/btn"
              onClick={() => handleButtonClick(item.primaryButton.url)}
            >
              <i
                className={`${item.primaryButton.icon} mr-2 group-hover/btn:scale-110 transition-transform duration-300`}
                aria-hidden="true"
              ></i>
              {primaryButtonText}
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 hover:scale-105 transform transition-all duration-300 ease-spring hover:bg-secondary hover:text-secondary-foreground group/btn"
              onClick={() => handleButtonClick(item.secondaryButton.url)}
            >
              <i
                className={`${item.secondaryButton.icon} mr-2 group-hover/btn:scale-110 transition-transform duration-300`}
                aria-hidden="true"
              ></i>
              {secondaryButtonText}
            </Button>
          </div> */}
        </div>
      </CardContent>
    </Card>
  );

  const renderGridLayout = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {sectionData.items.map((item, index) => (
        <div
          key={item.id}
          className="animate-fade-in"
          style={{ animationDelay: `${index * 0.15}s` }}
        >
          {renderCard(item, index)}
        </div>
      ))}
    </div>
  );

  const renderCarouselLayout = () => (
    <div className="relative">
      <div className="overflow-hidden rounded-xl" ref={emblaRef}>
        <div className="flex gap-4">
          {sectionData.items.map((item, index) => (
            <div
              key={item.id}
              className="flex-[0_0_90%] sm:flex-[0_0_85%] md:flex-[0_0_80%] lg:flex-[0_0_360px] min-w-0"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {renderCard(item, index)}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-center mt-8 gap-3">
        <Button
          variant="outline"
          size="sm"
          onClick={scrollPrev}
          disabled={!canScrollPrev}
          className="w-12 h-12 rounded-full p-0 bg-background/80 backdrop-blur-sm hover:bg-primary hover:text-primary-foreground transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform"
          aria-label={`${sectionType} anterior`}
        >
          <ChevronLeft className="w-5 h-5" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={scrollNext}
          disabled={!canScrollNext}
          className="w-12 h-12 rounded-full p-0 bg-background/80 backdrop-blur-sm hover:bg-primary hover:text-primary-foreground transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform"
          aria-label={`Próximo ${sectionType}`}
        >
          <ChevronRight className="w-5 h-5" />
        </Button>
      </div>

      {/* Progress indicator */}
      <div className="flex justify-center mt-4 gap-2">
        {sectionData.items.map((_, index) => (
          <div
            key={index}
            className="w-2 h-2 rounded-full bg-muted transition-all duration-300 hover:bg-primary cursor-pointer"
            onClick={() => emblaApi?.scrollTo(index)}
          />
        ))}
      </div>
    </div>
  );

  return (
    <section className="mb-12 animate-fade-in">
      <div className="text-center mb-8 lg:mb-12">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground mb-3">
          {sectionData.title}
        </h2>
        <p className="text-muted-foreground max-w-md lg:max-w-2xl mx-auto text-sm sm:text-base leading-relaxed">
          {sectionData.description}
        </p>
      </div>

      {layout === "grid" ? renderGridLayout() : renderCarouselLayout()}
    </section>
  );
};

export default GenericCardSection;
