import React from "react";
import { ContactData } from "./types";

interface ContactInfoProps {
  contactData: ContactData;
  userName: string;
}

export const ContactInfo = ({ contactData, userName }: ContactInfoProps) => {
  // Extract email from links
  const emailLink = contactData.links.find((link) =>
    link.url.startsWith("mailto:")
  );
  const email = emailLink?.url.replace("mailto:", "");

  return (
    <div className="text-center mt-40">
      <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-6">
        {userName}
      </h2>
      <div className="text-gray-300 text-lg space-y-2">
        {emailLink && (
          <p>
            <a
              className="hover:text-white transition-colors"
              href={emailLink.url}
            >
              {email}
            </a>
          </p>
        )}
      </div>
    </div>
  );
};
