// ============================================================================
// CORE INTERFACES - Based on actual JSON data structure
// ============================================================================

/**
 * Button interface used throughout the application
 */
export interface Button {
  icon: string
  url: string
}

/**
 * Button configuration interface for sections
 */
export interface ButtonConfig {
  primaryButtonText?: string
  secondaryButtonText?: string
  showBadge?: boolean
}

/**
 * User information interface
 */
export interface User {
  avatar: string
  bio: string
  name: string
  username: string
  heroImage: string
  brandLogo: string
}

/**
 * Link interface for the main links array
 */
export interface Link {
  classIcon: string
  text: string
  url: string
}

/**
 * Social media link interface
 */
export interface SocialMediaLink {
  classIcon: string
  text: string
  url: string
}

/**
 * Color settings interface
 */
export interface ColorSettings {
  background: string
  linkText: string
  primary: string
  secondary: string
  socialIconBackground: string
}

/**
 * SEO and page settings interface
 */
export interface Settings {
  colors: ColorSettings
  favicon: string
  pageDescription: string
  pageKeywords: string
  ogImage: string
  backgroundVideo: string
}

/**
 * Gallery image interface
 */
export interface GalleryImage {
  alt: string
  description: string
  id: number
  title: string
  url: string
}

/**
 * Gallery section interface
 */
export interface Gallery {
  description: string
  enabled: boolean
  images: GalleryImage[]
  title: string
}

/**
 * Review interface
 */
export interface Review {
  comment: string
  id: number
  name: string
  photo: string
  rating: number
}

/**
 * Reviews section interface
 */
export interface ReviewsSection {
  description: string
  enabled: boolean
  reviews: Review[]
  title: string
}

/**
 * Video section interface
 */
export interface VideoSection {
  description: string
  enabled: boolean
  title: string
  youtubeUrl: string
}

/**
 * Location data interface
 */
export interface LocationData {
  enabled: boolean
  address: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  contact: {
    phone: string
    whatsapp: string
  }
  hours: {
    weekdays: string
    weekends: string
    closed?: string
  }
  googleMapsUrl: string
}

/**
 * Team member interface
 */
export interface TeamMember {
  name: string
  photo: string
  role: string
  url: string
}

/**
 * Team section interface
 */
export interface TeamSection {
  enabled: boolean
  title: string
  members: TeamMember[]
}

/**
 * Section item interface - used in features, services, and generic sections
 */
export interface SectionItem {
  description: string
  id: number
  image?: string
  primaryButton: Button
  secondaryButton: Button
  title: string
}

/**
 * Features section interface
 */
export interface FeaturesSection {
  description: string
  enabled: boolean
  items: SectionItem[]
  title: string
  buttonConfig?: ButtonConfig
}

/**
 * Services section interface
 */
export interface ServicesSection {
  description: string
  enabled: boolean
  items: SectionItem[]
  title: string
  buttonConfig?: ButtonConfig
}

/**
 * Generic section interface
 */
export interface GenericSection {
  description: string
  enabled: boolean
  items: SectionItem[]
  title: string
  buttonConfig?: ButtonConfig
}

// ============================================================================
// MAIN USER PROFILE INTERFACE
// ============================================================================

/**
 * Complete user profile interface matching the JSON data structure
 */
export interface UserProfile {
  featuresSection: FeaturesSection
  gallery: Gallery
  genericSection: GenericSection
  links: Link[]
  location?: LocationData
  phone: string
  reviews: ReviewsSection
  servicesSection: ServicesSection
  settings: Settings
  socialMedia: SocialMediaLink[]
  team?: TeamSection
  user: User
  video: VideoSection
  /**
   * Optional business model type that customizes UI (e.g., icons)
   * Supported values: 'gym' | 'tattoo' | 'barber' | 'general'
   * Unknown or missing values should be treated as 'general'.
   */
  model?: string
}

// ============================================================================
// API INTERFACES
// ============================================================================

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export type UserProfileResponse = ApiResponse<UserProfile>

// ============================================================================
// LEGACY INTERFACES (for backward compatibility)
// ============================================================================

/**
 * @deprecated Use Link interface instead
 */
export interface UserLink {
  id: string
  title: string
  url: string
  description?: string
  isActive: boolean
  order: number
  icon?: string
  clickCount?: number
}

export interface CreateUserRequest {
  username: string
  displayName: string
  bio?: string
  avatar?: string
}

export interface UpdateUserRequest extends Partial<CreateUserRequest> {
  id: string
}

export interface CreateLinkRequest {
  userId: string
  title: string
  url: string
  description?: string
  icon?: string
}

export interface UpdateLinkRequest extends Partial<CreateLinkRequest> {
  id: string
}

export interface UserStats {
  totalUsers: number
  totalLinks: number
  totalClicks: number
}

export interface ApiError {
  code: string
  message: string
  details?: Record<string, unknown>
}

// ============================================================================
// ERROR CLASSES
// ============================================================================

export class UserNotFoundError extends Error {
  constructor(username: string) {
    super(`User '${username}' not found`)
    this.name = 'UserNotFoundError'
  }
}

export class InvalidUsernameError extends Error {
  constructor(username: string) {
    super(`Invalid username: '${username}'`)
    this.name = 'InvalidUsernameError'
  }
}
