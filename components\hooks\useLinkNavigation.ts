import { useCallback } from "react";

export const useLinkNavigation = () => {
    const handleLinkClick = useCallback(
        (url: string, event: React.MouseEvent) => {
            try {
                if (
                    url.startsWith("https") ||
                    url.startsWith("mailto:") ||
                    url.startsWith("tel:")
                ) {
                    window.open(url, "_blank", "noopener,noreferrer");
                } else if (url.startsWith("#")) {
                    event.preventDefault();
                    const element = document.querySelector(url);
                    if (element) {
                        element.scrollIntoView({
                            behavior: "smooth",
                            block: "start",
                            inline: "nearest",
                        });
                    }
                }
            } catch (error) {
                console.error("Error opening link:", error);
            }
        },
        []
    );

    return { handleLinkClick };
};
