// Constants for admin data service
export const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
export const MAX_DESCRIPTION_LENGTH = 160
export const MAX_KEYWORDS_COUNT = 10
export const HEX_COLOR_REGEX = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
export const PHONE_REGEX = /^[\+]?[1-9][\d]{0,15}$/
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
export const DEFAULT_API_BASE_URL = 'https://cardlink-bio-default-rtdb.firebaseio.com/users'

// Error messages
export const ERROR_MESSAGES = {
  USER_NOT_FOUND: 'User data not found',
  SECTION_NOT_FOUND: 'Section not found',
  ITEMS_ARRAY_NOT_FOUND: 'Items array not found',
  ITEM_NOT_FOUND: 'Item not found',
  INVALID_HEX_COLOR: 'Please enter a valid hex color (e.g., #FF0000)',
  INVALID_PHONE: 'Please enter a valid phone number',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_URL: 'Please enter a valid URL',
  DESCRIPTION_TOO_LONG: `Page description should be ${MAX_DESCRIPTION_LENGTH} characters or less for SEO`,
  TOO_MANY_KEYWORDS: `Please limit to ${MAX_KEYWORDS_COUNT} keywords or less, separated by commas`,
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  DATA_SAVED: 'Data saved successfully',
  ITEM_CREATED: 'Item created successfully',
  ITEM_UPDATED: 'Item updated successfully',
  ITEM_DELETED: 'Item deleted successfully',
} as const
