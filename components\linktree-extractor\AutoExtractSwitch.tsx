"use client";

import React from "react";
import { Switch } from "@/components/ui/switch";
import { FormField, FormLabel, FormDescription } from "@/components/ui/form";
import { Zap, Info } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface AutoExtractSwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}

export default function AutoExtractSwitch({
  checked,
  onChange,
  disabled = false,
}: AutoExtractSwitchProps) {
  return (
    <FormField>
      <div className="flex items-center justify-between p-4 border rounded-lg bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-950/20 dark:to-yellow-950/20 border-orange-200 dark:border-orange-800">
        <div className="flex items-start gap-3">
          <div className="flex items-center gap-2 mt-1">
            <Zap className="w-4 h-4 text-orange-500" />
            <Switch
              id="auto-extract"
              checked={checked}
              onCheckedChange={onChange}
              disabled={disabled}
              className="data-[state=checked]:bg-orange-500"
            />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <FormLabel htmlFor="auto-extract" className="text-sm font-medium">
                Extract data with FireCrawl before submitting
              </FormLabel>
              <Badge variant="secondary" className="text-xs">
                <Info className="w-3 h-3 mr-1" />
                Recommended
              </Badge>
            </div>
            <FormDescription className="mt-1">
              When enabled, the system will first extract data from the Linktree page before processing
            </FormDescription>
          </div>
        </div>
      </div>
    </FormField>
  );
}