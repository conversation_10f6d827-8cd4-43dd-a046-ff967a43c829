"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ExternalLink, HelpCircle, MapPinIcon, Zap } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface QuickActionsProps {
  onOpenGPlaces: () => void;
  onShowHelp?: () => void;
  disabled?: boolean;
  className?: string;
}

export default function QuickActions({
  onOpenGPlaces,
  onShowHelp,
  disabled = false,
  className,
}: QuickActionsProps) {
  return (
    <TooltipProvider>
      <div className={`flex items-center gap-2 ${className}`}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="lg"
              onClick={onOpenGPlaces}
              disabled={disabled}
              className="flex items-center rounded-xl gap-2 hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-blue-950/50"
            >
              <span className="hidden sm:inline">Abrir GPlaces</span>
              <MapPinIcon className="w-4 h-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Open GPlaces in a new tab</p>
          </TooltipContent>
        </Tooltip>

        {onShowHelp && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={onShowHelp}
                disabled={disabled}
                className="hover:bg-muted"
              >
                <HelpCircle className="w-4 h-4" />
                <span className="sr-only">Show help</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Get help with the form</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  );
}
