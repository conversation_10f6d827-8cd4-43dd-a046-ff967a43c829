import { LucideIcon } from 'lucide-react'

// Type definitions for admin data service
export interface DataSection {
  id: string
  name: string
  type: 'array' | 'object'
  fields: DataField[]
  itemsKey?: string // Key name for the items array (e.g., 'items', 'images', 'reviews', 'members')
  icon?: LucideIcon // Icon component
  description?: string // Section description
}

export interface DataField {
  key: string
  label: string
  type: 'string' | 'number' | 'boolean' | 'url' | 'email' | 'textarea' | 'select'
  required?: boolean
  options?: string[] // For select fields
  validation?: (value: unknown) => string | null
}

export type SectionData = Record<string, unknown>
export type ItemData = Record<string, unknown> & { id?: string | number }

export interface CrudOperation {
  type: 'create' | 'read' | 'update' | 'delete'
  section: string
  itemId?: string | number
  data?: ItemData
}

export interface CrudResult<T = unknown> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
}
