import FireCrawlApp from '@mendable/firecrawl-js';
import { z } from 'zod';

// Zod schema for structured data extraction
const schema = z.object({
  extra_info: z.object({
    location: z.string().optional(),
    username: z.string().optional(),
    description: z.string().optional(),
    avatar_url: z.string().optional(),
    user_bio: z.string().optional()
  }).optional(),
  links: z.array(z.object({
    title: z.string().optional(),
    url: z.string(),
    icon: z.string().optional()
  }))
});

// Type for the extraction result
export interface LinktreeExtractionResult {
  success: boolean;
  data?: {
    extra_info?: {
      location?: string;
      username?: string;
      description?: string;
      avatar_url?: string;
      user_bio?: string;
    };
    links: Array<{
      title?: string;
      url: string;
      icon?: string;
    }>;
  };
  error?: string;
}

export async function extractLinktreeData(url: string): Promise<LinktreeExtractionResult> {
  // Get API key from environment variable for security
  const apiKey = process.env.FIRECRAWL_API_KEY || process.env.NEXT_PUBLIC_FIRECRAWL_API_KEY;

  if (!apiKey) {
    return {
      success: false,
      error: 'Firecrawl API key not found. Please set FIRECRAWL_API_KEY or NEXT_PUBLIC_FIRECRAWL_API_KEY environment variable.'
    };
  }

  try {
    const app = new FireCrawlApp({ apiKey });

    // Convert Zod schema to JSON Schema format for Firecrawl
    const jsonSchema = {
      type: "object",
      properties: {
        extra_info: {
          type: "object",
          properties: {
            location: { type: "string" },
            username: { type: "string" },
            description: { type: "string" },
            avatar_url: { type: "string" },
            user_bio: { type: "string" }
          },
          additionalProperties: false
        },
        links: {
          type: "array",
          items: {
            type: "object",
            properties: {
              title: { type: "string" },
              url: { type: "string" },
              icon: { type: "string" }
            },
            required: ["url"],
            additionalProperties: false
          }
        }
      },
      required: ["links"],
      additionalProperties: false
    };

    // Use the correct extract method according to Firecrawl v1 API
    const extractResult = await app.extract([url], {
      prompt: "Extract all links and their titles and suggested/recommended icons name (following Lucid React Icons pattern) from this Linktree page (except Linktree own links). Look for social media links, website links, contact information, and any other clickable buttons or links. Also extract profile information including username, bio/description, location, and avatar image URL if present.",
      schema: jsonSchema,
    });

    // Handle the response according to Firecrawl API structure
    if (extractResult.success && extractResult.data) {
      // Validate the extracted data with Zod schema
      const validatedData = schema.parse(extractResult.data);
      return {
        success: true,
        data: validatedData
      };
    } else {
      return {
        success: false,
        error: extractResult.error || 'Failed to extract data from Linktree page'
      };
    }
  } catch (error) {
    console.error('Firecrawl extraction error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred during extraction'
    };
  }
}
