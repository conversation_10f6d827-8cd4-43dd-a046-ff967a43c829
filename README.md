# AvencaLink - Linktree-style Web Application

A modern, responsive Linktree-style web application built with Next.js 15, TypeScript, and Tailwind CSS. Create beautiful landing pages for all your links with just one URL.

## 🚀 Features

- **Dynamic User Profiles**: Create personalized profile pages with custom usernames
- **Responsive Design**: Mobile-first design that looks great on all devices
- **Link Management**: Add, organize, and track clicks on your links
- **Custom Themes**: Personalize your page with custom colors and button styles
- **Social Media Integration**: Connect your social media profiles
- **Analytics**: Track profile views and link clicks
- **SEO Optimized**: Proper meta tags and Open Graph support
- **Fast Performance**: Built with Next.js 15 and optimized for speed

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui
- **Package Manager**: PNPM
- **Fonts**: Geist Sans & Geist Mono

## 📁 Project Structure

```
avencalink/
├── app/                    # Next.js App Router
│   ├── [username]/        # Dynamic user profile pages
│   ├── api/               # API routes
│   ├── login/             # Authentication pages
│   ├── signup/
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── ui/               # UI components (buttons, etc.)
│   ├── link-card.tsx     # Link card component
│   └── user-profile.tsx  # User profile component
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions
├── services/             # API service layer
├── types/                # TypeScript type definitions
└── public/               # Static assets
```

## 🚀 Getting Started

1. **Install dependencies**:
   ```bash
   pnpm install
   ```

2. **Run the development server**:
   ```bash
   pnpm dev
   ```

3. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📖 Usage

### Viewing Profiles

- Visit the homepage at `/`
- Check out the demo profile at `/demo`
- Access any user profile at `/[username]`

### API Endpoints

- `GET /api/users/[username]` - Get user profile data
- `POST /api/users/[username]/view` - Track profile view
- `POST /api/links/[linkId]/click` - Track link click
- `GET /api/stats` - Get platform statistics

### Example User Profiles

The application includes sample user profiles:
- `/demo` - Demo user with sample links
- `/johndoe` - Developer profile example

## 🎨 Customization

### Themes

Users can customize their profiles with:
- Background colors
- Text colors
- Link button colors
- Button styles (rounded, square, pill)

### Link Types

Support for various link types:
- Website links
- Social media profiles
- Documents and files
- Custom URLs

## 🔧 Development

### Adding New Components

1. Create component in `components/` directory
2. Export from appropriate index file
3. Import and use in your pages

### API Development

1. Create route handlers in `app/api/`
2. Define TypeScript interfaces in `types/`
3. Update service layer in `services/`

### Styling

- Use Tailwind CSS classes
- Follow the design system in `globals.css`
- Utilize CSS variables for theming

## 📱 Responsive Design

The application is built mobile-first with breakpoints:
- Mobile: Default (< 640px)
- Tablet: `sm:` (≥ 640px)
- Desktop: `md:` (≥ 768px)
- Large: `lg:` (≥ 1024px)

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms

The application can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- UI components from [Shadcn/ui](https://ui.shadcn.com/)
- Icons from [Heroicons](https://heroicons.com/)
- Fonts from [Vercel](https://vercel.com/font)
