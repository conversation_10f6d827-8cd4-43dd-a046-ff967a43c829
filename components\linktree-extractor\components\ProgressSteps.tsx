"use client";

import React from "react";
import { CheckCircle, Circle, ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface Step {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  active: boolean;
}

interface ProgressStepsProps {
  steps: Step[];
  className?: string;
}

export default function ProgressSteps({ steps, className }: ProgressStepsProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <h3 className="text-sm font-medium text-muted-foreground mb-3">Progress</h3>
      <div className="space-y-3">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              {step.completed ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <Circle className={cn(
                  "w-5 h-5",
                  step.active ? "text-blue-500" : "text-muted-foreground"
                )} />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <p className={cn(
                "text-sm font-medium",
                step.completed ? "text-green-700 dark:text-green-400" : 
                step.active ? "text-blue-700 dark:text-blue-400" : 
                "text-muted-foreground"
              )}>
                {step.title}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                {step.description}
              </p>
            </div>
            {index < steps.length - 1 && (
              <ArrowRight className="w-4 h-4 text-muted-foreground/50 mt-0.5" />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}