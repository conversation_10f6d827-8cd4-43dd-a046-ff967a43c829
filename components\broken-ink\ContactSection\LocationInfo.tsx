import React from "react";
import { LocationData } from "./types";

interface LocationInfoProps {
  locationData: LocationData;
}

export const LocationInfo = ({ locationData }: LocationInfoProps) => {
  if (!locationData.enabled) {
    return null;
  }

  return (
    <>
      {/* Hours Information */}
      {locationData.hours && (
        <div className="space-y-2 mt-16">
          <div
            role="heading"
            aria-level={2}
            className="text-white text-xl font-semibold mb-4 flex justify-center"
          >
            Horário de Funcionamento
          </div>
          {locationData.hours.weekdays && (
            <div className="flex justify-center gap-4">
              <span className="text-gray-300">
                {locationData.hours.weekdays}
              </span>
            </div>
          )}
          {locationData.hours.weekends && (
            <div className="flex justify-center gap-4">
              <span className="text-gray-300">
                {locationData.hours.weekends}
              </span>
            </div>
          )}
          {locationData.hours.closed && (
            <div className="flex justify-center gap-4">
              <span className="text-gray-400">
                {locationData.hours.closed}:
              </span>
              <span className="text-gray-300 font-bold">Fechado</span>
            </div>
          )}
        </div>
      )}

      {/* Location Information */}
      <div className="text-center my-16">
        <div
          role="heading"
          aria-level={2}
          className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4"
        >
          {locationData.title}
        </div>
        {locationData.description && (
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            {locationData.description}
          </p>
        )}
        {locationData.address && (
          <p className="text-gray-400 text-base mt-2">{locationData.address}</p>
        )}

        {/* Google Maps Link */}
        {locationData.googleMapsUrl && (
          <div className="text-center mt-6">
            <a
              href={locationData.googleMapsUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-6 py-3 rounded-lg transition-colors text-gray-300 hover:text-white"
            >
              Ver no Google Maps
              <i className="fa fa-map-marker"></i>
            </a>
          </div>
        )}
      </div>
    </>
  );
};
